<ul class="breadcrumb">
   <li><a href="<?php echo site_url('avatars');?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('event/event_dashboard');?>">Events</a></li>
  <li><a href="<?php echo site_url('event');?>">Manage Event</a></li>
  <li><a href="<?php echo site_url('event/event_details/'.$event_id);?>">Event Detail</a></li>
  <li><a href="<?php echo site_url('event/register_student/'.$event_id);?>">Event List</a></li>
  <li>Add/View Student</li>
</ul>

<div class="col-md-12">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px;">
        <div class="d-flex justify-content-between" style="width:100%;">
          <h3 class="card-title panel_title_new_style_staff">
            <a class="back_anchor" href="<?php echo site_url('event/register_student/'.$event_id);?>">
              <span class="fa fa-arrow-left"></span>
            </a> 
            Add Students
          </h3>
          <span>
            <a data-toggle="modal" data-target="#summary" class="new_circleShape_res" style="background-color: #fe970a;cursor: pointer;">
                <span data-placement="top" data-toggle="tooltip" data-original-title="Add Participants" class="fa fa-plus" style="font-size: 19px;"></span>
            </a>
          </span>
        </div>
      </div>
    </div>
    <div class="card-body pt-1">
      <div class="mb-3">
        <span>
          <strong style="font-size: 16px;"><?php echo $event_details->event_name?></strong>
          <span style="font-size: 16px;"></span>&nbsp;&nbsp;&nbsp;&nbsp;
            <span><i class="fa fa-calendar" style="color: #6893ca;"></i>&nbsp;&nbsp;<?php echo date('d-m-Y',strtotime($event_details->reg_start_date)); ?></span>&nbsp;&nbsp;&nbsp;&nbsp;<span><i class="fa fa-clock-o" style="color: #ff9800;"></i>&nbsp;&nbsp;<?php echo date('H:i A',strtotime($event_details->reg_start_date)); ?>&nbsp;&nbsp;to&nbsp;&nbsp;<?php echo date('H:i A',strtotime($event_details->reg_end_date)); ?></span>
        </span>
      </div>

      <?php if(empty($event_student)) { ?>
        <h4 class="text-muted">Student list not found</h4>
      <?php } else { 
          foreach ($event_student as $key => $registration) { ?>
            <div class="col-md-3" id="student_<?php echo $registration->id ?>" style="padding:5px;">
              <div class="names" style="border:1px solid #000;">
                <div style="width: 90%;padding: 5px 10px;">
                 <b><?php echo $registration->class_name.''.$registration->section_name; ?>: &nbsp;&nbsp;</b><?php echo $registration->student_name; ?>
                </div>
                <div onclick="removeParticipant(<?php echo $registration->id ?>)" data-toggle="tooltip" data-original-title="Remove Student" class="text-center" style="border-radius:0px 10px 10px 0px;width: 10%;cursor: pointer;background: #ccc;padding: 5px 10px;">
                  <i class="fa fa-times"></i>
                </div>
              </div>
            </div>
          <?php } 
          ?>
      <?php } ?>
    </div>
  </div>
</div>

<div id="summary" class="modal fade" role="dialog">
  <div class="modal-dialog" style="width:60%;margin:auto;top:15%">
  <form method="post" id="invitees-form" action="<?php echo site_url('event/insert_student_data');?>" data-parsley-validate="">
        <!-- Modal content-->
        <div class="modal-content">
            <div class="modal-header" style="border-bottom: 2px solid #ccc;">
              <h4 class="modal-title" id="modalHeader">Add Invitees</h4>
              <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;</button>
            </div>
            <div id="invitees-input" class="modal-body form-horizontal">
              <input type="hidden" name="cur_participants" id="cur_participants" value="">
              <input type="hidden" name="event_id" value="<?php echo $event_id ?>">
              <!-- Hidden field to store selected sub-events -->
              <input type="hidden" name="sub_event_check" id="sub_event_check_hidden" value="">
              <div class="form-group">
                  <label class="control-label col-md-3" for="type">Type</label>
                  <div class="col-md-6"> 
                    <select class="form-control" name="type" id="type" onchange="changeType()">
                        <option value="">Select Type</option>
                        <option value="class_section">Class Section</option>
                        <option value="student">Student</option>
                    </select>
                  </div>
              </div>
              <div class="form-group" id="class_section">
                  <label class="control-label col-md-3" for="cs">Class/Section</label>
                  <div class="col-md-6"> 
                    <select class="form-control" name="class_section" id="cs" required="">
                      <option value="">Select Section</option>
                    </select>
                  </div>
              </div>
             
              <div id="student" style="display: none;">
                <div class="form-group">
                    <label class="control-label col-md-3" for="std_sections">Section</label>
                    <div class="col-md-6"> 
                      <select class="form-control" name="std_sections" id="std_sections" onchange="getSectionStudents()">
                        <option value="">Select Section</option>
                        <?php foreach ($sections as $key => $section) {
                          echo '<option value="'.$section->id.'">'.$section->class_name.''.$section->section_name.'</option>';
                        } ?>
                      </select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label col-md-3" for="std">Student</label>
                    <div class="col-md-6"> 
                      <select size="8" multiple="" class="form-control" name="students[]" id="std">
                        <option value="">Select Students</option>
                      </select>
                    </div>
                </div>
              </div>

              <!-- Sub Events Section - Shows immediately when #std is populated (same as parent registration view) -->
              <div id="sub-events-section" style="display: none;">
                <hr>
                <div class="panel panel-default">
                  <div class="panel-body">
                    <h3><b style="border-bottom: 3px solid #ff781b;" id="sub-event-header"></b></h3>
                    <input type="hidden" id="no_of_perssion_allowed_for_sub_event_registered" value="">
                    <table class="table no-border" id="sub-events-table">
                      <tbody id="sub-events-list"></tbody>
                    </table>
                    <h5 style="color: red;" id="alertAllowedCheckbox"></h5>
                  </div>
                </div>
              </div>
            </div>
            <div class="modal-footer">
              <button style="width:120px;" type="button" id="cancelModal" class="btn btn-danger" data-dismiss="modal">Cancel</button>
              <input style="width:120px;" type="button" id="confirmBtn" onclick="confirm_student_list()" class="btn btn-primary" data-dismiss="modal" value="Confirm">
            </div>
        </div>
      </form>
    </div>
</div>



<script type="text/javascript">
  function confirm_student_list(){
    $("#confirmBtn").prop('disabled', true).val('Please wait...');

    // Collect selected sub-events before form submission
    var selectedSubEvents = [];
    $('.single-checkbox:checked').each(function() {
      selectedSubEvents.push($(this).val());
    });

    // Store selected sub-events in hidden field
    $('#sub_event_check_hidden').val(JSON.stringify(selectedSubEvents));

    var $form = $('#invitees-form');
    if ($form.parsley().validate()){
        $form.submit();
    } else {
      $("#confirmBtn").prop('disabled', false).val('Confirm');
    }
  }

  function removeParticipant(register_id) {
    $.ajax({
      url:'<?php echo site_url('event/remove_event_student') ?>',
      type:'post',
      data: {'register_id':register_id},
      success : function(data){
        if(data == 1) {
          $(function(){
            new PNotify({
                title: 'Success',
                text: 'Removed',
                type: 'success',
            });
          });
          location.reload();
        } else {
          $(function(){
            new PNotify({
                title: 'Error',
                text: 'Something went wrong...',
                type: 'error',
            });
          });
        }
      }
    });
  }


    function getSections() {
      $.ajax({
        url:'<?php echo site_url('event/get_all_class_section_list') ?>',
        type:'post',
        success : function(data){
          var section_list = JSON.parse(data);
          var options = '<option value="">Select Section</option>';
          for (var i = 0; i < section_list.length; i++) {
            options += '<option value="'+section_list[i].id+'">'+section_list[i].class_name+''+section_list[i].section_name+'</option>';
          }
          $("#cs").html(options);
        }
      });
    }

  
    function getSectionStudents() {
      var section_id = $("#std_sections").val();
      $("#std").html('');
      if(section_id == '')
        return false;
      $.ajax({
          url:'<?php echo site_url('event/getSectionwiseStudents') ?>',
          type:'post',
          data: {'section_id':section_id, 'event_id': $('input[name="event_id"]').val()},
          success : function(data){
              var students = JSON.parse(data);
              var options = '<option value="">Select Student</option>';
              var registeredCount = 0;
              var availableCount = 0;

              for (var i = 0; i < students.length; i++) {
                // Check if student is already registered (disabled)
                if (students[i].disabled == 1) {
                  options += '<option value="'+students[i].id+'" disabled style="color: #dc3545; background-color: #f8d7da; font-style: italic;">'+students[i].stdName+' ⚠️ Already Registered</option>';
                  registeredCount++;
                } else {
                  options += '<option value="'+students[i].id+'">'+students[i].stdName+'</option>';
                  availableCount++;
                }
              }

              $("#std").html(options);

              // Show notification about registration status
              var notificationHtml = '';
              if (students.length > 0) {
                if (registeredCount > 0) {
                  notificationHtml = '<div class="alert alert-warning mt-2" style="padding: 8px 12px; font-size: 13px;">' +
                    '<i class="fa fa-info-circle"></i> ' +
                    '<strong>Section Summary:</strong> ' + students.length + ' total students (' +
                    '<span class="text-success">' + availableCount + ' available</span>, ' +
                    '<span class="text-danger">' + registeredCount + ' already registered</span>)' +
                    '</div>';
                } else {
                  notificationHtml = '<div class="alert alert-success mt-2" style="padding: 8px 12px; font-size: 13px;">' +
                    '<i class="fa fa-check-circle"></i> ' +
                    '<strong>All ' + students.length + ' students are available for registration</strong>' +
                    '</div>';
                }
              }

              // Remove existing notification and add new one
              $("#std").parent().find('.alert').remove();
              $("#std").after(notificationHtml);
          }
      });
    }

    function changeType() {
      var type = $("#type").val();
      if(type == 'class_section') {
        $("#class_section").show();
        $("#cs").prop('required',true);
        $("#staff").hide();
        $("#stf").prop('required',false);
        $("#student").hide();
        $("#std").prop('required',false);
        $("#group").hide();
        $("#grp_members").prop('required',false);
        getSections();
      } else if(type == 'student') {
        $("#class_section").hide();
        $("#cs").prop('required',false);
        $("#staff").hide();
        $("#stf").prop('required',false);
        $("#student").show();
        $("#std").prop('required',true);
        $("#group").hide();
        $("#grp_members").prop('required',false);
        getSections();
      }
    }

    // When students are selected in #std multi-select, show sub-events (same as parent registration view)
    $(document).on('change', '#std', function() {
      var selectedStudents = $(this).val();
      if (selectedStudents && selectedStudents.length > 0) {
        // Load sub-events immediately (same as parent registration view)
        loadSubEvents();
      } else {
        $('#sub-events-section').hide();
      }
    });

    // When type is changed, check if we need to show sub-events
    $(document).on('change', '#type', function() {
      var selectedType = $(this).val();
      if (selectedType === 'class_section') {
        // Show sub-events for class section selection
        loadSubEvents();
      } else {
        $('#sub-events-section').hide();
      }
    });

    // When class section is selected (for class_section type), show sub-events
    $(document).on('change', '#cs', function() {
      var selectedType = $('#type').val();
      var selectedSection = $(this).val();
      if (selectedType === 'class_section' && selectedSection) {
        // Load sub-events immediately (same as parent registration view)
        loadSubEvents();
      } else if (selectedType === 'class_section') {
        $('#sub-events-section').hide();
      }
    });

    // Handle sub-event checkbox selection (same as parent registration view)
    $(document).on('change', '.single-checkbox', function() {
      var maxAllowed = parseInt($('#no_of_perssion_allowed_for_sub_event_registered').val());
      var checkedCount = $('.single-checkbox:checked').length;

      if (checkedCount > maxAllowed) {
        $(this).prop('checked', false);
        $('#alertAllowedCheckbox').text('You can select maximum ' + maxAllowed + ' sub-events.');
      } else {
        $('#alertAllowedCheckbox').text('');
      }
    });

    // Function to load sub-events when students are selected (same as parent registration view)
    function loadSubEvents() {
      var eventId = $('input[name="event_id"]').val();

      if (!eventId) {
        return;
      }

      $.ajax({
        url: '<?php echo site_url("event/get_registration_details"); ?>',
        type: 'POST',
        data: {
          event_id: eventId
        },
        dataType: 'json',
        success: function(response) {
          if (response.success && response.sub_event && response.sub_event.length > 0) {
            displaySubEvents(response.sub_event, response.event);
          } else {
            $('#sub-events-section').hide();
          }
        },
        error: function(xhr, status, error) {
          console.error('Error loading sub-events:', error);
          $('#sub-events-section').hide();
        }
      });
    }

    // Function to display sub-events (exact same as parent registration view)
    function displaySubEvents(subEvents, event) {
      $('#sub-events-section').show();
      $('#sub-event-header').text(event.sub_event_header || 'Sub Events');
      $('#no_of_perssion_allowed_for_sub_event_registered').val(event.sub_event_select_number || 1);

      var subEventHtml = '';
      subEvents.forEach(function(subEvent) {
        var disabled = '';
        var className = '';
        if (subEvent.max_registrations_allowed <= (subEvent.regCount || 0)) {
          disabled = 'disabled';
          className = 'registration_full';
        }

        subEventHtml += '<tr class="' + className + '">' +
          '<td><input ' + disabled + ' class="single-checkbox" type="checkbox" name="sub_event_check[]" value="' + subEvent.id + '"></td>' +
          '<td>' + subEvent.sub_event_name + '<br>' + subEvent.sub_event_description + '</td>' +
          '<td>';

        if (subEvent.sub_event_date) {
          subEventHtml += '<i class="fa fa-calendar" style="color: #ff9800;"></i>&nbsp;&nbsp;' +
            '<span>' + subEvent.sub_event_date + ' ' + (subEvent.start_time_of_registration || '') + '</span>';
        }

        subEventHtml += '</td></tr>';
      });

      $('#sub-events-list').html(subEventHtml);
    }


</script>

<style type="text/css">
  .names {
    border: 1px solid #ccc;
    margin-bottom: 10px;
    border-radius: 10px;
    display: flex;
  }
.new_circleShape_res {
    padding: 8px;
    border-radius: 50% !important;
    color: white !important;
    font-size: 22px;
    height: 3.2rem !important;
    width: 3.2rem !important;
    text-align: center;
    vertical-align: middle;
    float: left;
    border: none !important;
    box-shadow: 0px 3px 7px #ccc;
    line-height: 1.7rem !important;
}
.btn, .form-control {
  border-radius: .5rem;
}
.row {
  margin: 0px !important;
}
.nav-btn {
    background-color: #344a66 !important;
    color: white !important;
    font-size: 1.3em !important;
    font-weight: 600 !important;
  }
  .btn-cus {
    display: inline-block;
    border-radius: 50%;
    padding: 5px 15px;
    box-shadow: 0px 2px 6px #ccc;
  }
  .btn-cus .fa{
    margin: 0px;
  }
  .collapse {
    visibility: visible;
    display: block;
  }
  .card-link {
    cursor: pointer;
    border-radius: 8px !important;

  }
  .modal-header, .bc{
    background-color: white !important;
    box-shadow: none;
    border-bottom: none;
    border-top-right-radius: 8px;
    border-top-left-radius: 8px;
    padding-bottom: 2px;
  }
  .card{
    border-radius: 8px;
  }
  .card-body{
    padding: 0px 1.25rem;
  }
  .allotted {
    background-color: #27c3ae;
    color: white;
    font-weight: 600;
  }
  .registration_full {
    background-color: #f8f9fa;
    color: #6c757d;
  }
  .registration_full input {
    cursor: not-allowed;
  }

  /* Enhanced styles for disabled students (already registered) */
  select option:disabled {
    color: #dc3545 !important;
    background-color: #f8d7da !important;
    font-style: italic;
    opacity: 0.8;
    cursor: not-allowed;
  }

  select option[disabled] {
    color: #dc3545 !important;
    background-color: #f8d7da !important;
    font-style: italic;
    opacity: 0.8;
    cursor: not-allowed;
  }

  /* Additional styling for the student select dropdown */
  #std {
    border-radius: 6px;
    border: 1px solid #ced4da;
    padding: 8px 12px;
    font-size: 14px;
  }

  #std:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    outline: 0;
  }

  /* Warning indicator for select with disabled options */
  #std:has(option:disabled) {
    border-left: 4px solid #ffc107;
  }

  /* Multi-select styling for disabled options */
  select[multiple] option:disabled {
    color: #999 !important;
    background-color: #f5f5f5 !important;
    font-style: italic;
    cursor: not-allowed;
  }

  /* Hover effect for disabled options */
  select option:disabled:hover {
    background-color: #f5f5f5 !important;
    color: #999 !important;
  }

  /* Additional styling for better visibility */
  select[multiple] option:disabled::before {
    content: "🚫 ";
    color: #dc3545;
  }
</style>