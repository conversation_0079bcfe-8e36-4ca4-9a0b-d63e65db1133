<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('itari/itari_menu'); ?>">ITARI</a></li>
  <li><a class="active">Report</a></li>
</ul>

<div class="col-md-12">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px;">
                <div class="d-flex justify-content-between" style="width:100%;">
                    <h3 class="card-title panel_title_new_style_staff">
                        <a class="back_anchor" href="<?php echo site_url('itari/itari_menu'); ?>">
                          <span class="fa fa-arrow-left"></span>
                        </a> 
                        ITARI Application Report
                    </h3>   
                    <div class="col-md-8 d-flex align-items-center justify-content-end">
                    <div class="col-md-3">
                            <select  class="form-control custom-select"  id="predefined_data" name="predefined_data" onchange="select_feild_names()">
                              <option value="">Select Predefined data</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
        <div class="card-body">
         
          <form enctype="multipart/form-data" id="itari_report" autocomplete="off" class="form-horizontal" data-parsley-validate method="post">
             
            <div class="col-md-3">
                <div class="form-group">
                    <label class="col-md-4 control-label" for="grade">Program</label>
                    <select class="col-md-8 form-control" name="grade" id="grade">
                        <option value="">All</option>
                        <option value="pgde">PGDE(I)</option>
                        <option value="maedrpl">M.A.Ed. RPL</option>
                        <option value="cidtl">CIDTL</option>
                        <option value="pgpece">PGP in ECE</option>
                    </select>
                </div>
            </div>

            <div class="col-md-3">
                <div class="form-group">
                    <label class="col-md-4 control-label" for="classsection">Select Field(s)</label>  
                    <div class="col-md-8">
                    <select id="fields" name="fields[]" required="" class="form-control input-md" multiple="" size="8">
                      <?php foreach ($columnList as $column) { ?>
                        <option value="<?php echo $column['index']; ?>">
                          <?php echo str_replace('_', ' ', $column['displayName']); ?>
                        </option>
                      <?php } ?>
                    </select>
                    </div>
                </div>
            </div>

            <div class="col-md-3">
              <div class="form-group">
                <label class="col-md-6 control-label" for="classsection"> Admission Status</label>  
                  <div class="col-md-6">
                    <select id="ad_status" name="ad_status" class="form-control">
                      <option value="">All</option>
                      <option value="Submitted">Submitted</option>
                      <option value="Admit">Admit</option>
                      <option value="Draft">Draft</option>
                    </select>
                  </div>
              </div>
            </div>

            <div class="col-md-3">
              <div class="form-group">
                <label class="col-md-6 control-label" for="classsection"> Fees Status</label>  
                  <div class="col-md-6">
                    <select id="ad_status" name="fee_status" class="form-control">
                      <option value="">All</option>
                      <option value="1">Fees Paid</option>
                      <option value="0">Fees Not Paid</option>
                    </select>
                  </div>
              </div>
            </div>

                <div class="col-md-12" style="margin: 20px 0 20px 0;">
                <center>
                  <button type="button" onclick="generate_report()" class="btn btn-primary" >Generate</button>
                  <button type="button" id="save_btn" data-toggle="modal" data-target="#save_field_name" class="btn btn-success" >Save as Report Template</button>
                  <button type="button" id="update_btn" data-toggle="modal" data-target="#update_field_name" style="display: none;" class="btn btn-warning" >Update Report Template</button>
                </center>
              </div>  

              </form>
          </div>
          <div style="display: none;" id="itari_report_data">
                <div class="col-md-12 mb-3 d-flex align-items-center justify-content-end">
                  <ul class="panel-controls" id="excel">
                      <a style="margin-right:3px;" onclick="exportToExcel()" class="btn btn-primary pull-right">Export to excel</a>
                  </ul>
                </div>
                <div class="card-body">
                  <ul class="panel-controls" style="margin-right:10px;width: 300px;" id="range-input"></ul>
                <div class="table-responsive" id="export_itari">
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="save_field_name" tabindex="-1" role="dialog" style="width:35%;margin:auto;top:25%" data-backdrop="static" aria-labelledby="save_field_name-label" aria-hidden="true">
  <div class="modal-content modal-dialog" style="border-radius: 8px;">
    <div class="modal-header" style="border-bottom: 2px solid #ccc;">
      <h4 class="modal-title" id="modalHeader">Add Title</h4>
      <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;</button>
    </div>
    <div class="modal-body">
      <form id="titleModalform">
        <div class="col-md-12">
          <div class="form-group">
            <label  class="col-md-4">Title Name<font color="red">*</font></label>
            <div class="col-md-6">
                <input id="title" class="form-control" placeholder="Enter title" name="title" class="form-control" type="text" maxlength="100" required/>
            </div>
          </div>
        </div>
      </form> 
    </div> 
    <div class="modal-footer">
          <button class="btn btn-primary" id="submit" type="button" onclick="save_itari_fields()">Submit </button>
    </div>
  </div>
</div>

<div class="modal fade" id="update_field_name" tabindex="-1" role="dialog" style="width:35%;margin:auto;top:25%" data-backdrop="static" aria-labelledby="update_field_name-label" aria-hidden="true">
  <div class="modal-content modal-dialog" style="border-radius: 8px;">
    <div class="modal-header" style="border-bottom: 2px solid #ccc;">
      <h4 class="modal-title" id="modalHeader">Update Title</h4>
      <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;</button>
    </div>
    <div class="modal-body">
    <form id="updateModalform">
      <input type="hidden" id="update_id" name="update_id">
      <div class="col-md-12">
        <div class="form-group">
          <label  class="col-md-4">Title Name<font color="red">*</font></label>
          <div class="col-md-6">
              <input id="update_title" class="form-control" placeholder="Enter title" name="title" class="form-control" type="text" maxlength="100" />
          </div>
        </div>
      </div>
    </form> 
    </div> 
    <div class="modal-footer">
          <button class="btn btn-primary" id="submit" type="button" onclick="update_itari_fields()">Update</button>
    </div>
  </div>
</div>

<script>
  $(document).ready(function() {
    get_predefined_names();
  });

  function get_predefined_names() {
    $.ajax({
        url: '<?php echo site_url('itari/Itari_Controller/get_predefined_names'); ?>',
        type: 'post',
        success: function(data) {
            var field_data = JSON.parse(data);
            console.log(field_data);

            if (field_data.predefined_data.length > 0) {
                var html = '';
                html += '<option>Select Predefined data</option>';

                var len = field_data.predefined_data.length;

                for (var i = 0; i < len; i++) {
                    var dataItem = field_data.predefined_data[i];
                    html += '<option value="' + dataItem.id + '">' + dataItem.saved_report_name + '</option>';
                }

                $('#predefined_data').html(html);
            }
        }
    });
  }

  function save_itari_fields(){
    var form = $('#titleModalform');
        if (!form.parsley().validate()) {
            return 0;
        }
    var fied_names = $('#fields').val();
    var class_list = $('#grade').val();
    var ad_status = $('#ad_status').val();
    var fee_status = $('#fee_status').val();
    var title = $('#title').val();
    $.ajax({
      url:'<?php echo site_url('itari/Itari_Controller/save_itari_fields'); ?>',
      type:'post',
      data: {'fied_names':fied_names,'class_list':class_list,'ad_status':ad_status,'title':title, 'fee_status':fee_status},
      success: function(data) {
        var field_data = JSON.parse(data);
        if(field_data == 1){
          $("#save_field_name").modal('hide');
        }
      },
      complete:function(){
        get_predefined_names();
      }
    });
  }

  function update_itari_fields(){
    var form = $('#updateModalform');
    if (!form.parsley().validate()) {
        return 0;
    }
    var id = $('#update_id').val();
    var fied_names = $('#fields').val();
    var class_list = $('#grade').val();
    var ad_status = $('#ad_status').val();
    var title = $('#update_title').val();
    $.ajax({
      url:'<?php echo site_url('itari/Itari_Controller/update_itari_fields'); ?>',
      type:'post',
      data: {'id':id,'fied_names':fied_names,'class_list':class_list,'ad_status':ad_status,'title':title},
      success: function(data) {
        var field_data = JSON.parse(data);
        if(field_data == 1){
          $("#update_field_name").modal('hide');
        }
      },
      complete: function () {
        get_predefined_names();
      }
    });
  }

  function select_feild_names(){
    var columnList = <?php echo json_encode($columnList) ?>;
    var id = $('#predefined_data').val();
    console.log(id);
    $.ajax({
      url:'<?php echo site_url('itari/Itari_Controller/get_predefined_data'); ?>',
      type:'post',
      data:{'id':id},
      success: function(data) {
        var field_data = JSON.parse(data);
        console.log(field_data);
        if (field_data && field_data.filters_selected) {
            var filters = JSON.parse(field_data.filters_selected);

            // Assuming columnList is already defined elsewhere in your code
            $('#fields').html(_construct_fields(filters.field_id, columnList));
            $('#grade').html(_construct_grades(filters.grade));
            $('#ad_status').html(_construct_status(filters.status));
            
            // Assuming field_data.id and field_data.title are present
            $('#update_id').val(field_data.id);
            $('#update_title').val(field_data.saved_report_name);
            $('#save_btn').hide();
            $('#update_btn').show();
        }
      }
    });    
  }

  function _construct_fields(field_Id,columnList){
    var field_Id_arr = field_Id.split(',');
    console.log(field_Id_arr);
    console.log(columnList);
    var len = columnList.length;
    
    var html = '';
    var selectedFee = '';
    for(var i in columnList){
      var selected = '';
      selectedFee = '';
      for(j=0;j<field_Id_arr.length;j++){
        if(columnList[i].index == field_Id_arr[j]) {
          selected = 'selected';
        }
      }
      
      html +='<option '+selected+' value="'+columnList[i].index+'">'+columnList[i].displayName+'</option>';
    }
    return html;
  }

  function _construct_grades(grade) {
    var selectElement = document.getElementById('grade');
    var options = selectElement.options;
    var gradesarr = [];

    // Extract existing options to gradesarr
    for (var i = 1; i < options.length; i++) { // Start from 1 to skip the "All" option
        gradesarr.push({
            class_name: options[i].value,
            display_name: options[i].text
        });
    }

    var grade_output = '<option value="">All</option>';
    for (var i = 0; i < gradesarr.length; i++) {
        var selected = '';
        if (gradesarr[i].class_name === grade) {
            selected = 'selected';
        }
        grade_output += '<option ' + selected + ' value="' + gradesarr[i].class_name + '">' + gradesarr[i].display_name + '</option>';
    }

    return grade_output;
  }

  function _construct_status(status) {
    var selectElement = document.getElementById('ad_status');
    var options = selectElement.options;
    var statusArr = [];

    // Extract existing options to statusArr
    for (var i = 1; i < options.length; i++) { // Start from 1 to skip the "All" option
        statusArr.push({
            user_status: options[i].value,
            display_name: options[i].text
        });
    }

    var status_output = '<option value="">All</option>';
    for (var i = 0; i < statusArr.length; i++) {
        var selected = '';
        if (statusArr[i].user_status === status) {
            selected = 'selected';
        }
        status_output += '<option ' + selected + ' value="' + statusArr[i].user_status + '">' + statusArr[i].display_name + '</option>';
    }

    return status_output;
  }

  function exportToExcel() {
    var htmls = "";
    var uri = 'data:application/vnd.ms-excel;base64,';
    var template = '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--><meta http-equiv="content-type" content="text/plain; charset=UTF-8"/></head><body><table>{table}</table></body></html>';
    var base64 = function(s) {
      return window.btoa(unescape(encodeURIComponent(s)))
    };

    var format = function(s, c) {
      return s.replace(/{(\w+)}/g, function(m, p) {
        return c[p];
      })
    };

    var mainTable = $("#export_itari").html();


    htmls = mainTable;

    var ctx = {
      worksheet: 'Spreadsheet',
      table: htmls
    }


    var link = document.createElement("a");
    link.download = "export.xls";
    link.href = uri + base64(format(template, ctx));
    link.click();

  }

  function generate_report(){
    var fields = $('#fields').val();
    if(fields == null){
      return false;
    }
    var form = $('#itari_report')[0];
    var formData = new FormData(form);
    console.log(form);
    $.ajax({
      url:'<?php echo site_url('itari/Itari_Controller/get_itari_report'); ?>',
      type:'post',
      data: formData,
      processData: false,
      contentType: false,
      success: function(data) {
        $('#admission_report_data').show();
        var itari_data = JSON.parse(data)
        if(itari_data.length != 0){
          console.log(itari_data);
          if(itari_data){
            $('#export_itari').html(construct_admission_report(itari_data));
            $("#itari_report_data").css('display', 'block');
            add_scroller('export_itari');
          }
        }else{
          $("#itari_report_data").css('display', 'block');
          $('#excel').hide();
          $('#export_itari').html('<h3 class="no-data-display">Result not found</h3>')
        }
       
      }
    });
  }

  function construct_admission_report(itari_data) {
    let html = '';
    html += '<table class="table table-bordered" style="white-space: nowrap; text-overflow: ellipsis; overflow: hidden;">';
    html += '<thead>';
    html += '<tr>';
    html += '<th>#</th>';

    // Extract headers from the first object keys
    const headers = Object.keys(itari_data[0]);
    headers.forEach(header => {
        let displayHeader = header.toUpperCase().replaceAll("_", " ");
        if (header.endsWith("_merged")) {
            displayHeader = displayHeader.replace(" MERGED", "");
        }
        html += `<th>${displayHeader}</th>`;
    });
    
    html += '</tr>';
    html += '</thead>';
    html += '<tbody>';

    itari_data.forEach((row, index) => {
        html += '<tr>';
        html += `<td>${index + 1}</td>`;

        headers.forEach(header => {
            html += `<td style="max-width: 200px; white-space: normal;">${row[header]}</td>`;
        });

        html += '</tr>';
    });

    html += '</tbody>';
    html += '</table>';

    return html;
  }
</script>