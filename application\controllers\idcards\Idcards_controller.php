<?php
//echo "<pre>";print_r($data);die();
defined('BASEPATH') or exit('No direct script access allowed');

class Idcards_controller extends CI_Controller{
	public function __construct(){
		parent::__construct();
		$this->load->model('idcards/Idcards_model');
		$this->load->model('idcards/excel_model');
		$this->load->library('filemanager');
        // $this->load->library('card_generator');
        if (!$this->authorization->isModuleEnabled('ID_CARDS')) {
            redirect('dashboard', 'refresh');
          }
	}

	public function index() {
        $site_url = site_url();
	    $data['tiles'] = array(
            [
	            'title' => 'Manage Templates',
	            'sub_title' => 'View the Template and Add, Edit Template',
	          	'icon' => 'svg_icons/view.svg',
	            'url' => $site_url.'idcards',
	            'permission' => $this->authorization->isAuthorized('ID_CARDS.MANAGE_ID_CARD_TEMPLATE')
            ],
	        [
	            'title' => 'Manage Orders',
	            'sub_title' => 'Create a new order',
	          	'icon' => 'svg_icons/freshentry.svg',
	            'url' => $site_url.'idcards/Idcards_controller/create_order',
	            'permission' => $this->authorization->isAuthorized('ID_CARDS.MANAGE_ID_CARD_ORDERS')
            ],
            [
	            'title' => 'Download Approved ID Cards',
	            'sub_title' => 'View the Template and Add, Edit Template',
	          	'icon' => 'svg_icons/view.svg',
	            'url' => $site_url.'download',
                'permission' => $this->authorization->isAuthorized('ID_CARDS.MANAGE_ID_CARD_TEMPLATE')
            ],
			[
	            'title' => 'Manage Orders (Admin)',
	            'sub_title' => 'Manage Orders',
	          	'icon' => 'svg_icons/view.svg',
	            'url' => $site_url.'idcards/Idcards_controller/manage_order',
	            'permission' => $this->authorization->isSuperAdmin()
            ],
			[
	            'title' => 'Approve ID Cards',
	            'sub_title' => 'Approve ID Cards',
	          	'icon' => 'svg_icons/view.svg',
	            'url' => $site_url.'idcards/Idcards_controller/class_teacher_id_card_approval',
	            'permission' => $this->authorization->isAuthorized('ID_CARDS.APPROVE_ID_CARDS')
            ]
	    );
	    $data['tiles'] = checkTilePermissions($data['tiles']);

		$data['main_content'] = 'idcard/index';
		$this->load->view('inc/template',$data);
    }

    public function template_list(){
        $data['templates'] = $this->Idcards_model->get_all_templates();
        foreach ($data['templates'] as $key => $val) {
            $val->front_image_url = '';
            if(!empty($val->front_image_path)){
                $val->front_image_url = $this->filemanager->getFilePath($val->front_image_path);
            }
        }
		$data['main_content'] = 'idcard/template_list';
    	$this->load->view('inc/template',$data);
    }

    public function create_template() {
        // echo "<pre>"; print_r($this->input->post('front_design'));die();
        if ($this->input->post()) {
            $this->form_validation->set_rules('template_name', 'Template Name', 'required');
            $this->form_validation->set_rules('id_card_for', 'ID Card For', 'required');
            $this->form_validation->set_rules('unit_price', 'Unit Price', 'required|numeric');

            if ($this->form_validation->run() == FALSE) {
				$data['main_content'] = 'idcard/create_template';
				$this->load->view('inc/template',$data);

            } else {
                // Get front and back design JSON data
                $front_design_json = $this->input->post('front_design');

                $back_design_json = $this->input->post('back_design');

                // Create temporary files for the PNG images
                $front_temp_file = tempnam(sys_get_temp_dir(), 'front_design');
                $back_temp_file = tempnam(sys_get_temp_dir(), 'back_design');

                // Convert front JSON to PNG image
                $front_png_data = $this->convertJsonToPng($front_design_json);
                file_put_contents($front_temp_file, $front_png_data);

                // Convert back JSON to PNG image
                $back_png_data = $this->convertJsonToPng($back_design_json);
                file_put_contents($back_temp_file, $back_png_data);

                // Prepare files for S3 upload
                $front_s3_file = array(
                    'tmp_name' => $front_temp_file,
                    'name' => 'front_design_' . time() . '.png'
                );

                $back_s3_file = array(
                    'tmp_name' => $back_temp_file,
                    'name' => 'back_design_' . time() . '.png'
                );

                // Upload PNG files to S3
                $front_upload_result = $this->s3FileUpload($front_s3_file, 'idcards_template');
                $back_upload_result = $this->s3FileUpload($back_s3_file, 'idcards_template');

                // Clean up temporary files
                @unlink($front_temp_file);
                @unlink($back_temp_file);

                // Prepare template data with S3 file paths
                $template_data = array(
                    'name' => $this->input->post('template_name'),
                    'size' => $this->input->post('template_size'),
                    'id_card_for' => $this->input->post('id_card_for'),
                    'unit_price' => $this->input->post('unit_price'),
                    'status' => 'pending',
                    'front_design' => $front_design_json, // Keep original JSON for backward compatibility
                    'back_design' => $back_design_json, // Keep original JSON for backward compatibility
                    'front_image_path' => $front_upload_result['file_name'], // Store S3 file path
                    'back_image_path' => $back_upload_result['file_name'], // Store S3 file path
                    'created_at' => date('Y-m-d H:i:s'),
                );


                $template_id = $this->Idcards_model->save_template($template_data);

                if ($template_id) {
                    $this->session->set_flashdata('success', 'Template created successfully and pending approval!');
                    redirect('idcards');
                } else {
                    $this->session->set_flashdata('error', 'Failed to create template!');
					$data['main_content'] = 'idcard/create_template';
					$this->load->view('inc/template',$data);
                }
            }
        } else {
			$data['main_content'] = 'idcard/create_template';
			$this->load->view('inc/template',$data);
        }
    }

    public function update_template_design(){
        $front_design_json = $this->input->post('front_design');

        $back_design_json = $this->input->post('back_design');

        // Create temporary files for the PNG images
        $front_temp_file = tempnam(sys_get_temp_dir(), 'front_design');
        $back_temp_file = tempnam(sys_get_temp_dir(), 'back_design');

        // Convert front JSON to PNG image
        $front_png_data = $this->convertJsonToPng($front_design_json);
        file_put_contents($front_temp_file, $front_png_data);

        // Convert back JSON to PNG image
        $back_png_data = $this->convertJsonToPng($back_design_json);
        file_put_contents($back_temp_file, $back_png_data);

        // Prepare files for S3 upload
        $front_s3_file = array(
            'tmp_name' => $front_temp_file,
            'name' => 'front_design_' . time() . '.png'
        );

        $back_s3_file = array(
            'tmp_name' => $back_temp_file,
            'name' => 'back_design_' . time() . '.png'
        );

        // Upload PNG files to S3
        $front_upload_result = $this->s3FileUpload($front_s3_file, 'idcards_template');
        $back_upload_result = $this->s3FileUpload($back_s3_file, 'idcards_template');

        // Clean up temporary files
        @unlink($front_temp_file);
        @unlink($back_temp_file);

        // Prepare template data with S3 file paths
        $template_data = array(
            'front_design' => $front_design_json, // Keep original JSON for backward compatibility
            'back_design' => $back_design_json, // Keep original JSON for backward compatibility
            'front_image_path' => $front_upload_result['file_name'], // Store S3 file path
            'back_image_path' => $back_upload_result['file_name'], // Store S3 file path
        );


        $template_id = $this->Idcards_model->update_template($this->input->post('template_id'),$template_data);
        if ($template_id) {
            echo "1";
        } else {
            echo "0";
        }
    }

    public function downloadApprovedIDCards(){
        $data['orders_list']=$this->Idcards_model->getAllOrders();

        $data['main_content'] = 'idcard/downloadidcards';
		$this->load->view('inc/template',$data);

    }

    public function edit_template($id) {
        $data['template'] = $this->Idcards_model->get_template($id);


        if (empty($data['template'])) {
            $this->session->set_flashdata('error', 'Template not found!');
            redirect('idcards');
        }

        if ($this->input->post()) {
            $this->form_validation->set_rules('template_name', 'Template Name', 'required');
            $this->form_validation->set_rules('id_card_for', 'ID Card For', 'required');
            $this->form_validation->set_rules('unit_price', 'Unit Price', 'required|numeric');

            if ($this->form_validation->run() == FALSE) {
                $data['main_content'] = 'idcard/edit_template';
                $this->load->view('inc/template',$data);
            } else {
                // Get front and back design JSON data
                $front_design_json = $this->input->post('front_design');
               
                $back_design_json = $this->input->post('back_design');

                // Create temporary files for the PNG images
                $front_temp_file = tempnam(sys_get_temp_dir(), 'front_design');
                $back_temp_file = tempnam(sys_get_temp_dir(), 'back_design');

                // Convert front JSON to PNG image
                $front_png_data = $this->convertJsonToPng($front_design_json);
                file_put_contents($front_temp_file, $front_png_data);

                // Convert back JSON to PNG image
                $back_png_data = $this->convertJsonToPng($back_design_json);
                file_put_contents($back_temp_file, $back_png_data);

                // Prepare files for S3 upload
                $front_s3_file = array(
                    'tmp_name' => $front_temp_file,
                    'name' => 'front_design_' . time() . '.png'
                );

                $back_s3_file = array(
                    'tmp_name' => $back_temp_file,
                    'name' => 'back_design_' . time() . '.png'
                );

                // Upload PNG files to S3
                $front_upload_result = $this->s3FileUpload($front_s3_file, 'idcards_template');
                $back_upload_result = $this->s3FileUpload($back_s3_file, 'idcards_template');

                // Clean up temporary files
                @unlink($front_temp_file);
                @unlink($back_temp_file);

                $template_data = array(
                    'name' => $this->input->post('template_name'),
                    'size' => $this->input->post('template_size'),
                    'id_card_for' => $this->input->post('id_card_for'),
                    'unit_price' => $this->input->post('unit_price'),
                    'status' => 'pending', // Reset to pending after edit
                    'front_design' => $front_design_json, // Keep original JSON for backward compatibility
                    'back_design' => $back_design_json, // Keep original JSON for backward compatibility
                    // 'front_image_path' => $front_upload_result['file_name'], // Store S3 file path
                    // 'back_image_path' => $back_upload_result['file_name'], // Store S3 file path
                    'updated_at' => date('Y-m-d H:i:s')
                );

                $result = $this->Idcards_model->update_template($id, $template_data);

                if ($result) {
                    $this->session->set_flashdata('success', 'Template updated successfully and pending approval!');
                    redirect('edit/'.$id);
                } else {
                    $this->session->set_flashdata('error', 'Failed to update template!');
                    $data['main_content'] = 'idcard/edit_template';
			        $this->load->view('inc/template',$data);
                }
            }
        } else {
            $data['main_content'] = 'idcard/edit_template';
            $this->load->view('inc/template',$data);
        }
    }

    public function delete_template($id) {
        $result = $this->Idcards_model->delete_template($id);

        if ($result) {
            $this->session->set_flashdata('success', 'Template deleted successfully!');
        } else {
            $this->session->set_flashdata('error', 'Failed to delete template!');
        }

        redirect('idcards/Idcards_controller');
    }

    public function preview_template($id) {
        $data['template'] = $this->Idcards_model->get_template($id);
        if (empty($data['template'])) {
            $this->session->set_flashdata('error', 'Template not found!');
            redirect('idcard');
        }
        $data['main_content'] = 'idcard/preview_template';
        $this->load->view('inc/template',$data);
    }

    public function get_template_images() {
        $template_id = $this->input->post('template_id');
        $template = $this->Idcards_model->get_template($template_id);

        $response = array(
            'front_image' => '',
            'back_image' => ''
        );

        if ($template) {
            // Generate preview images or get paths to existing ones
            // This is a placeholder - you'll need to implement the actual image generation
            // based on your application's requirements

            // Example: If you have stored images or can generate them
            if (!empty($template->front_design)) {
                $response['front_image'] = base_url('assets/images/templates/front_' . $template_id . '.png');
            }

            if (!empty($template->back_design)) {
                $response['back_image'] = base_url('assets/images/templates/back_' . $template_id . '.png');
            }
        }

        header('Content-Type: application/json');
        echo json_encode($response);
    }

    public function upload_excel() {
        if ($this->input->post()) {
            $config['upload_path'] = './assets/uploads/excel/';
            $config['allowed_types'] = 'xlsx|xls|csv';
            $config['max_size'] = 2048;

            // Create directory if it doesn't exist
            if (!is_dir($config['upload_path'])) {
                mkdir($config['upload_path'], 0777, TRUE);
            }

            $this->upload->initialize($config);

            if (!$this->upload->do_upload('excel_file')) {
                $error = $this->upload->display_errors();
                $this->session->set_flashdata('error', $error);

                $data['templates'] = $this->Idcards_model->get_all_templates();

                $data['main_content'] = 'idcard/upload_excel';
                $this->load->view('inc/template',$data);
            } else {
                $upload_data = $this->upload->data();
                $file_path = $config['upload_path'] . $upload_data['file_name'];

                // Process Excel file
                $this->excel_model->process_excel($file_path);

                $this->session->set_flashdata('success', 'Excel data uploaded successfully!');
                redirect('cards');
            }
        } else {
            $data['templates'] = $this->Idcards_model->get_all_templates();
            $data['main_content'] = 'idcard/upload_excel';
            $this->load->view('inc/template',$data);
        }
    }

    public function preview_generation($id) {
        $template = $this->Idcards_model->get_template($id);
        if (!$template) {
            show_404();
        }

        // Get sample staff list - replace this with your actual staff data retrieval
        $staff_list = $this->Idcards_model->get_all_staffs();
        // $staff_list = array(
        //     (object)[
        //         'staff_name' => 'John Doe',
        //         'profile' => 'assets/images/sample_profile.jpg',
        //         'designation' => 'Software Engineer',
        //         'department' => 'IT Department',
        //         'employee_id' => 'EMP001'
        //     ],
        //     (object)[
        //         'staff_name' => 'Jane Smith',
        //         'profile' => 'assets/images/sample_profile.jpg',
        //         'designation' => 'HR Manager',
        //         'department' => 'Human Resources',
        //         'employee_id' => 'EMP002'
        //     ]
        // );

        $data['template'] = $template;
        $data['staff_list'] = $staff_list;
        $data['main_content'] = 'idcard/preview_generation';
        $this->load->view('inc/template', $data);
    }


    public function generate_cards() {
        $template = $this->Idcards_model->get_template(4);
        if (!$template) {
            show_404();
        }
        // Get staff details
        // $staff_list = $this->idcard_model->get_staff_list();
       $staff_list =  (object)[
            'staff_name' => 'John Doe',
            'profile' => 'assets/images/sample_profile.jpg',
            'designation' => 'Software Engineer',
            'department' => 'IT Department',
            'employee_id' => 'EMP001'
        ];

        // Generate cards
        $pdf = $this->card_generator->generate_cards($template, $staff_list);


        // Output PDF
        $pdf->Output('ID_Cards.pdf', 'D');

        // // $data['excel_data'] = $this->excel_model->get_excel_data();
        // // $data['excel_headers'] = $this->excel_model->get_excel_headers();
        // $data['templates'] = $this->Idcards_model->get_all_templates();

        // $data['main_content'] = 'idcard/generate_cards';
        // $this->load->view('inc/template',$data);
    }

    public function export_cards() {
        // Handle PDF generation and download
        $template_id = $this->input->post('template_id');
        $data_ids = $this->input->post('data_ids');

        // Load necessary data
        $template = $this->Idcards_model->get_template($template_id);
        $excel_data = $this->excel_model->get_excel_data();

        // Filter data based on selected IDs
        $selected_data = array();
        foreach ($data_ids as $id) {
            if (isset($excel_data[$id])) {
                $selected_data[] = $excel_data[$id];
            }
        }

        // Generate PDF
        $this->load->library('pdf');
        $pdf = $this->pdf->load();

        // Add pages based on template and data
        foreach ($selected_data as $person_data) {
            // Process and add card to PDF
            $pdf->AddPage();
            // Front side
            // Implementation depends on how template and data are structured

            $pdf->AddPage();
            // Back side
        }

        $pdf->Output('id_cards.pdf', 'D');
    }

    public function get_template_json($id) {
        $template = $this->Idcards_model->get_template($id);

        if (!$template) {
            $response = array('status' => 'error', 'message' => 'Template not found');
        } else {
            $response = array(
                'status' => 'success',
                'template' => array(
                    'id' => $template->id,
                    'name' => $template->name,
                    'size' => $template->size,
                    'front_design' => json_decode($template->front_design),
                    'back_design' => json_decode($template->back_design)
                )
            );
        }
        header('Content-Type: application/json');
        echo json_encode($response);
    }

    public function create_order(){
        $data['count_of_templates'] = $this->Idcards_model->get_count_of_templates();
        $data['total_orders'] = $this->Idcards_model->get_total_id_card_orders();
        $data['total_id_card_order_in_review'] = $this->Idcards_model->total_id_card_order_in_review();
        $data['total_id_card_order_in_printing'] = $this->Idcards_model->total_id_card_order_in_printing();
        $data['total_id_card_orders_completed'] = $this->Idcards_model->total_id_card_orders_completed();
		$data['main_content'] = 'idcard/create_order_view';
		$this->load->view('inc/template',$data);
	}


	public function createNewIDCardOrder($id=''){
        $data['order_id'] = $id;
        if($id!=''){
            $data['order_details'] = $this->Idcards_model->getOrderDetails($id);

            // If order is for staff_subset or student_subset, fetch the selected entities
            if (isset($data['order_details']->id_card_for_type) &&
                ($data['order_details']->id_card_for_type === 'staff_subset' ||
                 $data['order_details']->id_card_for_type === 'student_subset')) {

                $profile_type = $data['order_details']->id_card_for;
                $data['selected_entities'] = $this->Idcards_model->getSelectedEntities($id, $profile_type);
            }
        }
        $data['staff'] = $this->Idcards_model->getApprovedStaff();
		$data['main_content']='idcard/createNewIDCardOrder';
		$this->load->view('inc/template',$data);
	}

	public function orderSummaryIDcard($order_id){
        $data['order_details'] = $this->Idcards_model->getOrderDetails($order_id);
		$data['main_content']='idcard/orderSummaryIDcard';
		$this->load->view('inc/template',$data);
	}

    public function getDataOfProfiletypes(){
        $data = $this->Idcards_model->getDataOfProfiletypes();
        echo json_encode($data);
    }

    public function getTemplates(){
        $id_card_for = $this->input->get('id_card_for');

        // Pass the id_card_for parameter to the model
        $templates = $this->Idcards_model->get_all_templates_selected_idcards_for($id_card_for);

        echo json_encode($templates);
    }

    public function insertOrderPage1(){
        // echo "<pre>"; print_r($_POST); die();
        $data = $this->Idcards_model->insertOrderPage1();
        echo json_encode($data);
    }

    public function insertTemplateId(){
        $data = $this->Idcards_model->insertTemplateId();
        echo json_encode($data);
    }

    public function insertPlaceOrderDetails(){
        return $this->Idcards_model->insertPlaceOrderDetails();
    }

    public function getAllOrderDetails(){
        $data = $this->Idcards_model->getAllOrderDetails();
        echo json_encode($data);
    }

    public function getIdCardTemplate() {
        $order_id = $this->input->post('order_id');

        // Get order details to find the template ID
        $order_details = $this->Idcards_model->getOrderDetails($order_id);

        if (!$order_details || !$order_details->idcard_template_id) {
            echo json_encode(['error' => 'Template not found']);
            return;
        }

        // Get the template data
        $template = $this->Idcards_model->get_template($order_details->idcard_template_id);

        if (!$template) {
            echo json_encode(['error' => 'Template not found']);
            return;
        }

        // Return the template data
        echo json_encode([
            'success' => true,
            'template' => $template
        ]);
    }

    public function getEntityDetails() {
        $entity_id = $this->input->post('entity_id');
        $id_card_for = $this->input->post('id_card_for');

        // Get entity details based on id_card_for
        $entity = null;
        if (strtolower($id_card_for) === 'staff') {
            $entity = $this->Idcards_model->getStaffDetails($entity_id);
        } else {
            $entity = $this->Idcards_model->getStudentDetails($entity_id);
        }

        if (!$entity) {
            echo json_encode(['error' => 'Entity not found']);
            return;
        }

        // Return the entity data
        echo json_encode([
            'success' => true,
            'entity' => $entity
        ]);
    }

    public function order_details($order_id){
        $data['order_id'] = $order_id;
        $data['order_details'] = $this->Idcards_model->getOrderDetails($order_id);
        $template = $this->Idcards_model->get_template($data['order_details']->idcard_template_id);
        $data['template'] = $template->id;
        $data['staff_list'] = $this->Idcards_model->get_all_staffs();
        // echo "<pre>";print_r($data['staff_list']);die();

        // Get status counts for this order
        $data['status_counts'] = $this->Idcards_model->get_order_status_counts($order_id);

        $data['main_content']='idcard/order_details_view';
		$this->load->view('inc/template',$data);

    }

    public function change_idcard_entity_status(){

        $result = $this->Idcards_model->update_entity_status();

        if ($result) {
            // Get the updated entity data to return to the client
            $sm_id = $this->input->post('sm_id');
            $order_id = $this->input->post('order_id');
            $type = trim($this->input->post('type'));
            $status = trim($this->input->post('status'));

            // Return the updated entity data along with the success message
            echo json_encode([
                'success' => true,
                'message' => 'Status updated successfully',
                'entity_id' => $sm_id,
                'status' => $status,
                'type' => $type
            ]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to update status']);
        }
    }

    public function getAllEntitiesForTheSpecificOrder() {
        $order_id = $this->input->post('order_id');
        $id_card_for = $this->input->post('id_card_for');
        $status_filter = $this->input->post('status_filter', true) ?: 'all'; // Set default to 'all'

        $data = $this->Idcards_model->getAllEntitiesForTheSpecificOrder($order_id, $id_card_for, $status_filter);
        if (!empty($data) && is_array($data)) {
            foreach ($data as &$entity) {
                $entity->type = $id_card_for;
            }
        }
        echo json_encode($data);
    }

    /**
     * Get entities for specific order with chunked/paginated data retrieval
     * Supports pagination for better performance with large datasets
     */
    public function getEntitiesForOrderChunked() {
        try {
            // Get input parameters
            $order_id = $this->input->post('order_id');
            $id_card_for = $this->input->post('id_card_for');
            $status_filter = $this->input->post('status_filter', true) ?: 'all';
            $page = (int)$this->input->post('page', true) ?: 1;
            $limit = (int)$this->input->post('limit', true) ?: 50; // Default 50 records per chunk
            $search = $this->input->post('search', true) ?: '';
            
            // Validate required parameters
            if (!$order_id || !$id_card_for) {
                echo json_encode([
                    'success' => false,
                    'message' => 'Order ID and ID card type are required',
                    'data' => [],
                    'pagination' => []
                ]);
                return;
            }

            // Validate and limit chunk size for performance
            if ($limit > 100) {
                $limit = 100; // Maximum 100 records per chunk
            }

            // Calculate offset
            $offset = ($page - 1) * $limit;

            // Get chunked data from model
            $result = $this->Idcards_model->getEntitiesForOrderChunked(
                $order_id,
                $id_card_for,
                $status_filter,
                $limit,
                $offset,
                $search
            );
            // Add type information to each entity
            if (!empty($result['data']) && is_array($result['data'])) {
                foreach ($result['data'] as &$entity) {
                    $entity->type = $id_card_for;
                }
            }

            // Prepare response
            $response = [
                'success' => true,
                'data' => $result['data'] ?? [],
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $limit,
                    'total_records' => $result['total_count'] ?? 0,
                    'total_pages' => ceil(($result['total_count'] ?? 0) / $limit),
                    'has_more' => ($offset + $limit) < ($result['total_count'] ?? 0)
                ],
                'status_counts' => $result['status_counts'] ?? [],
                'message' => 'Data retrieved successfully'
            ];

            echo json_encode($response);

        } catch (Exception $e) {
            // Log error and return error response
            log_message('error', 'Error in getEntitiesForOrderChunked: ' . $e->getMessage());

            echo json_encode([
                'success' => false,
                'message' => 'An error occurred while retrieving data',
                'data' => [],
                'pagination' => [],
                'error' => $e->getMessage()
            ]);
        }
    }

    public function get_all_entities_for_the_specific_order_class_teacher() {
        $order_id = $this->input->post('order_id');
        $id_card_for = $this->input->post('id_card_for');
        $status_filter = $this->input->post('status_filter', true) ?: 'all'; // Set default to 'all'

        $data = $this->Idcards_model->get_all_entities_for_the_specific_order_class_teacher($order_id, $id_card_for, $status_filter);
        if (!empty($data) && is_array($data)) {
            foreach ($data as &$entity) {
                $entity->type = $id_card_for;
            }
        }
        echo json_encode($data);
    }

    public function approve_template() {
        $template_id = $this->input->post('template_id');

        if (!$template_id) {
            echo json_encode(['success' => false, 'message' => 'Template ID is required']);
            return;
        }

        $template_data = [
            'status' => 'approved',
            'approved_by' => $this->session->userdata('user_id'),
            'approved_at' => date('Y-m-d H:i:s')
        ];

        $result = $this->Idcards_model->update_template($template_id, $template_data);

        if ($result) {
            echo json_encode(['success' => true, 'message' => 'Template approved successfully']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to approve template']);
        }
    }

    public function reject_template() {
        $template_id = $this->input->post('template_id');
        $reason = $this->input->post('reason');

        if (!$template_id) {
            echo json_encode(['success' => false, 'message' => 'Template ID is required']);
            return;
        }

        if (!$reason) {
            echo json_encode(['success' => false, 'message' => 'Rejection reason is required']);
            return;
        }

        $template_data = [
            'status' => 'rejected',
            'rejected_by' => $this->session->userdata('user_id'),
            'rejected_at' => date('Y-m-d H:i:s'),
            'rejection_reason' => $reason
        ];

        $result = $this->Idcards_model->update_template($template_id, $template_data);

        if ($result) {
            echo json_encode(['success' => true, 'message' => 'Template rejected successfully']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to reject template']);
        }
    }

    public function aprrove_idcards_templates() {
        // Get the POST data
        $result = $this->Idcards_model->aprrove_idcards_templates();

        // Return JSON response based on the result
        if ($result === true) {
            echo json_encode([
                'success' => true,
                'message' => 'ID card approved successfully'
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'Failed to approve ID card'
            ]);
        }
    }

    public function downlaodcards() {
        $input = $this->input->post();
        $result = $this->Idcards_model->downlaodIDcards_order_wise($input);

        $processedImages = [];
        foreach ($result as $value) {
            $fullpath = $this->filemanager->getFilePath($value->page);
            $processedImage = $this->processImageForPdf($fullpath, 'landscape');
            $processedImages[] = $processedImage;
        }
        // Return JSON for AJAX display
        echo json_encode([
            'success' => true,
            'data' => $result
        ]);
    }

    public function download_idcards_pdf($order_id, $page_type, $orientation = 'portrait') {
        $input = $this->input->post();
        $result = $this->Idcards_model->downlaodIDcards_order_wise([
            'page' => $page_type,
            'order_id' => $order_id
        ]);

        if (empty($result)) {
            echo json_encode(['success' => false, 'message' => 'No ID cards found']);
            return;
        }

        $html = '<!DOCTYPE html><html><head>';
        $html .= '<meta charset="UTF-8">';
        $html .= '<style>
            @page {
                margin: 10mm;
                size: A4;
            }
            body {
                margin: 0;
                padding: 0;
                font-family: Arial, sans-serif;
            }
            .card-container {
                width: 100%;
                display: flex;
                flex-direction: column;
                align-items: center;
            }
            .card-row {
                display: flex;
                width: 100%;
                justify-content: space-around;
                margin-bottom: 20px;
                page-break-inside: avoid;
            }
            .card-item {
                background-color: #ffffff;
                border: 1px solid #dddddd;
                padding: 10px;
                text-align: center;
                margin: 5px;
                box-sizing: border-box;
                box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            }

            /* Clear fix for rows */
            .card-row:after {
                content: "";
                display: table;
                clear: both;
            }
        </style>';
        $html .= '</head><body>';
        $html .= '<div class="card-container">';

        // Process images first to handle rotation and conversion
        $processedImages = [];
        foreach ($result as $value) {
            $fullpath = $this->filemanager->getFilePath($value->page);

            // Process the image (rotate if needed and convert to PNG)
            $processedImage = $this->processImageForPdf($fullpath, $orientation);
            $processedImages[] = $processedImage;
        }

        // Log the number of images being processed
        error_log('Processing ' . count($processedImages) . ' images for PDF generation');

        $count = 0;
        foreach ($processedImages as $imageData) {
            // Start a new row for every 2 items
            if ($count % 2 == 0) {
                $html .= '<div class="card-row">';
            }

            $fullpath = $this->filemanager->getFilePath($value->page);
            $html .= '<div class="card-item">';
            $html .= '<img src="' . $fullpath . '" alt="ID Card">';
            $html .= '</div>';

            if ($count % 4 == 3 || $count == count($result) - 1) {
                $html .= '</div>';
            }
            $count++;
        }

        $html .= '</div></body></html>';
        // Remove debug output - this was preventing PDF generation
        // echo "<pre>"; print_r($html); die();
        $insert = $this->Idcards_model->updateIdcardHtml($html,$order_id, $page_type);

        if($insert){
            $path = $this->__generateidcard_pdf($insert['pdf_html'], $order_id, $page_type);

        }
        $maxRetries = 10;
        $retryDelay = 3;
        $retryCount = 0;

        $check_pdf_status = $this->Idcards_model->check_idcard_pdf_status($order_id, $page_type);

        while (
            ($page_type == 'front' && isset($check_pdf_status->front_page_pdf_status) && $check_pdf_status->front_page_pdf_status == 0) ||
            ($page_type == 'back' && isset($check_pdf_status->back_page_pdf_status) && $check_pdf_status->back_page_pdf_status == 0)
        ) {

            if ($retryCount >= $maxRetries) {
                echo json_encode(['success' => false, 'message' => 'PDF generation is taking too long. Try again later.']);
                return;
            }

            sleep($retryDelay);
            $check_pdf_status = $this->Idcards_model->check_idcard_pdf_status($order_id, $page_type);

            $retryCount++;
        }





            $url = $this->filemanager->getFilePath($path);
            $data = file_get_contents($url);
            $this->load->helper('download');
            force_download('idcards.pdf', $data, TRUE);

        exit;

    }



    public function get_order_status_counts_ajax() {
        $order_id = $this->input->post('order_id');

        if (!$order_id) {
            echo json_encode(['success' => false, 'message' => 'Order ID is required']);
            return;
        }

        $counts = $this->Idcards_model->get_order_status_counts($order_id);

        echo json_encode([
            'success' => true,
            'counts' => $counts
        ]);
    }

    // public function get_order_update_status_counts_ajax() {
    //     $order_id = $this->input->post('order_id');
    //     $type = $this->input->post('type');
    //     $entityId = $this->input->post('entityId');

    //     if (!$order_id) {
    //         echo json_encode(['success' => false, 'message' => 'Order ID is required']);
    //         return;
    //     }

    //     $counts = $this->Idcards_model->get_update_order_status_counts($order_id, $type, $entityId);

    //     echo json_encode([
    //         'success' => true,
    //         'counts' => $counts
    //     ]);
    // }


    public function get_entities_for_order() {
        $order_id = $this->input->post('order_id');
        $id_card_for = $this->input->post('id_card_for');
        $status_filter = $this->input->post('status_filter', true);

        if (!$order_id || !$id_card_for) {
            echo json_encode(['success' => false, 'message' => 'Order ID and ID card type are required']);
            return;
        }

        // Get entities from the model
        $entities = $this->Idcards_model->getAllEntitiesForTheSpecificOrder($order_id, $id_card_for, $status_filter);

        // Add type information to each entity
        if (!empty($entities) && is_array($entities)) {
            foreach ($entities as &$entity) {
                $entity->type = $id_card_for;
            }
        }

        echo json_encode([
            'success' => true,
            'data' => $entities
        ]);
    }

    public function getImageAsBase64() {
        header('Content-Type: application/json');

        $image_url = $this->input->post('image_url');

        if (!$image_url) {
            echo json_encode(['success' => false, 'message' => 'No image URL provided']);
            return;
        }

        try {
            // Initialize cURL session
            $ch = curl_init();

            // Set cURL options
            curl_setopt($ch, CURLOPT_URL, $image_url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);

            // Execute cURL session
            $image_data = curl_exec($ch);

            // Check for cURL errors
            if (curl_errno($ch)) {
                throw new Exception(curl_error($ch));
            }

            // Get content type
            $content_type = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);

            // Close cURL session
            curl_close($ch);

            // Convert to base64
            $base64 = 'data:' . $content_type . ';base64,' . base64_encode($image_data);

            echo json_encode(['success' => true, 'base64' => $base64]);

        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => 'Error fetching image: ' . $e->getMessage()
            ]);
        }
    }

    public function process_image() {
        header('Content-Type: application/json');

        $image_url = $this->input->post('image_url');
        $orientation = $this->input->post('orientation');

        if (!$image_url) {
            echo json_encode(['success' => false, 'message' => 'No image URL provided']);
            return;
        }

        try {
            // Initialize cURL session to fetch the image
            $ch = curl_init();

            // Set cURL options
            curl_setopt($ch, CURLOPT_URL, $image_url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);

            // Execute cURL session
            $image_data = curl_exec($ch);

            // Check for cURL errors
            if (curl_errno($ch)) {
                throw new Exception(curl_error($ch));
            }

            // Close cURL session
            curl_close($ch);

            // Create image from string data
            $image = @imagecreatefromstring($image_data);
            if (!$image) {
                throw new Exception('Invalid image data');
            }

            // Get image dimensions
            $width = imagesx($image);
            $height = imagesy($image);
            $rotated = false;

            // Check if rotation is needed based on orientation and dimensions
            if (($orientation === 'landscape' && $height > $width) ||
                ($orientation === 'portrait' && $width > $height)) {

                // Create a new image with swapped dimensions
                $rotated_image = imagecreatetruecolor($height, $width);

                // Rotate the image by copying pixels
                for ($x = 0; $x < $width; $x++) {
                    for ($y = 0; $y < $height; $y++) {
                        // Get pixel color
                        $color = imagecolorat($image, $x, $y);

                        if ($orientation === 'landscape') {
                            // Rotate 90 degrees clockwise
                            imagesetpixel($rotated_image, $y, $width - $x - 1, $color);
                        } else {
                            // Rotate 90 degrees counter-clockwise
                            imagesetpixel($rotated_image, $height - $y - 1, $x, $color);
                        }
                    }
                }

                // Free the original image
                imagedestroy($image);
                $image = $rotated_image;
                $rotated = true;
            }

            // Output buffer to capture PNG data
            ob_start();
            imagepng($image, null, 0); // Use highest quality (0 compression)
            $processed_image_data = ob_get_clean();

            // Clean up
            imagedestroy($image);

            // Convert to base64
            $base64 = 'data:image/png;base64,' . base64_encode($processed_image_data);

            echo json_encode([
                'success' => true,
                'image_data' => $base64,
                'rotated' => $rotated
            ]);

        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => 'Error processing image: ' . $e->getMessage()
            ]);
        }
    }

    public function manage_order(){
        $data['main_content'] = 'idcard/manage_order_view';
        $this->load->view('inc/template',$data);
    }


    private function __generateidcard_pdf($html, $order_id, $page_type, $orientation = 'portrait') {
        $school = CONFIG_ENV['main_folder'];
        $path = $school.'/idcards/'.uniqid().'-'.time().".pdf";

        $bucket = $this->config->item('s3_bucket');

        $status = $this->Idcards_model->updateGenertaedIdcardPath($path, $order_id, $page_type);
        $page = $orientation; // Use the orientation parameter
        $page_size = 'a4';
        $curl = curl_init();
        $postData = $html;
        $username = CONFIG_ENV['job_server_username'];
        $password = CONFIG_ENV['job_server_password'];
        $return_url = site_url() . 'Callback_Controller/updateGenertaedIdcardPathLink/' . $page_type;

        curl_setopt_array($curl, array(
            CURLOPT_URL => CONFIG_ENV['job_server_pdfgen_uri'],
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_USERPWD => $username . ":" . $password,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => "path=".$path."&bucket=".$bucket."&page=".$page."&page_size=".$page_size."&data=".$postData."&return_url=".$return_url,
            CURLOPT_HTTPHEADER => array(
                "Accept: application/json",
                "Cache-Control: no-cache",
                "Content-Type: application/x-www-form-urlencoded",
                "Postman-Token: 090abdb9-b680-4492-b8b7-db81867b114e"
            ),
        ));

        $response = curl_exec($curl);
        $err = curl_error($curl);
        curl_close($curl);
        return $path;
    }

    /**
     * Process an image for PDF generation - rotate if needed and convert to PNG
     *
     * @param string $image_url The URL of the image to process
     * @param string $orientation The desired orientation ('portrait' or 'landscape')
     * @return array Array containing processed image data and rotation status
     */
    private function processImageForPdf($image_url, $orientation = 'portrait') {
        try {
            // Initialize cURL session to fetch the image
            $ch = curl_init();

            // Set cURL options
            curl_setopt($ch, CURLOPT_URL, $image_url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);

            // Execute cURL session
            $image_data = curl_exec($ch);

            // Check for cURL errors
            if (curl_errno($ch)) {
                // Return original URL if error
                curl_close($ch);
                return [
                    'data_url' => $image_url,
                    'rotated' => false
                ];
            }

            // Close cURL session
            curl_close($ch);

            // Create image from string data
            $image = @imagecreatefromstring($image_data);
            if (!$image) {
                // Return original URL if invalid image
                return [
                    'data_url' => $image_url,
                    'rotated' => false
                ];
            }

            // Get image dimensions
            $width = imagesx($image);
            $height = imagesy($image);
            $rotated = false;

            // Check if rotation is needed based on orientation and dimensions
            if (($orientation === 'landscape' && $height > $width) ||
                ($orientation === 'portrait' && $width > $height)) {

                // Create a new image with swapped dimensions
                $rotated_image = imagecreatetruecolor($height, $width);

                // Rotate the image by copying pixels
                for ($x = 0; $x < $width; $x++) {
                    for ($y = 0; $y < $height; $y++) {
                        // Get pixel color
                        $color = imagecolorat($image, $x, $y);

                        if ($orientation === 'landscape') {
                            // Rotate 90 degrees clockwise
                            imagesetpixel($rotated_image, $y, $width - $x - 1, $color);
                        } else {
                            // Rotate 90 degrees counter-clockwise
                            imagesetpixel($rotated_image, $height - $y - 1, $x, $color);
                        }
                    }
                }

                // Free the original image
                imagedestroy($image);
                $image = $rotated_image;
                $rotated = true;
            }

            // Output buffer to capture PNG data
            ob_start();
            imagepng($image, null, 0); // Use highest quality (0 compression)
            $processed_image_data = ob_get_clean();

            // Clean up
            imagedestroy($image);

            // Convert to base64 for embedding in HTML
            $base64 = 'data:image/png;base64,' . base64_encode($processed_image_data);

            return [
                'data_url' => $base64,
                'rotated' => $rotated
            ];

        } catch (Exception $e) {
            // Return original URL if any exception occurs
            return [
                'data_url' => $image_url,
                'rotated' => false
            ];
        }
    }

    public function idcard_construct_pdf_html($result, $page,$orientation){
        $html = '<!DOCTYPE html><html><head>';
    $html .= '<meta charset="UTF-8">';

    // Set width and height based on orientation
    if ($orientation === 'landscape') {
        $cardWidth = 335;
        $cardHeight = 215;
        $imageWidth = 335;
        $imageHeight = 215;
        $imageRotation = '0deg'; // No rotation needed for landscape
    } else {
        $cardWidth = 335;
        $cardHeight = 215;
        $imageWidth = 215; // swapped
        $imageHeight = 335; // swapped
        if ($page === 'back') {
            $imageRotation = '90deg'; // rotate image to appear landscape
        }else{
            $imageRotation = '-90deg'; // rotate image to appear landscape
        }
    }
    

    $html .= '<style>
        @page {
            margin: 1mm;
            size: A4 ' . $orientation . ';
        }
        body {
            margin: 0;
            padding: 0;
        }
        .card-container {
            width: 100%;
            padding: 0;
            margin: 0;
        }
        .card-row {
            display: block;
            width: 100%;
            clear: both;
            page-break-inside: avoid;
            break-inside: avoid;
        }
        .card-item {
            width: ' . $cardWidth . 'px;
            height: ' . $cardHeight . 'px;
            border: 1px solid #ccc;
            float: left;
            margin-right: 7mm;
            margin-left: 7.5mm;
            margin-top: 1.5mm;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            position: relative;
        }
        .card-item img.rotate-landscape {
            width: ' . $imageWidth . 'px;
            height: ' . $imageHeight . 'px;
            transform: rotate(' . $imageRotation . ');
            transform-origin: center center;
            position: absolute;
            top: 50%;
            left: 50%;
            translate: -50% -50%;
        }
    </style>';
    
    $html .= '</head><body>';
    $html .= '<div class="card-container">';
    
    for ($i = 0; $i < count($result); $i += 2) {
        $html .= '<div class="card-row">';

        if ($page === 'back') {
            if (isset($result[$i + 1])) {
                $html .= '<div class="card-item"><img src="' . $result[$i + 1]->image_url . '" alt="ID Card" class="rotate-landscape"></div>';
                $html .= '<div class="card-item"><img src="' . $result[$i]->image_url . '" alt="ID Card" class="rotate-landscape"></div>';
            } else {
                $html .= '<div class="card-item" style="border: 1px solid #ccc;"></div>';
                $html .= '<div class="card-item"><img src="' . $result[$i]->image_url . '" alt="ID Card" class="rotate-landscape"></div>';
            }
        } else {
            $html .= '<div class="card-item"><img src="' . $result[$i]->image_url . '" alt="ID Card" class="rotate-landscape"></div>';
            if (isset($result[$i + 1])) {
                $html .= '<div class="card-item"><img src="' . $result[$i + 1]->image_url . '" alt="ID Card" class="rotate-landscape"></div>';
            }
        }

        $html .= '</div>';
    }

    $html .= '</div></body></html>';
    
    // Debug Output

    return $html;
    }

    public function generate_id_cards(){
        $input = $this->input->post();
        // Validate required inputs
        if (empty($input['page']) || empty($input['order_id']) || empty($input['orientation'])) {
            echo json_encode([
                'success' => false,
                'message' => 'Missing required parameters',
                'data' => []
            ]);
            return;
        }

        // Get approved ID cards for the order
        $result = $this->Idcards_model->downlaodIDcards_order_wise($input);

        foreach ($result as $key => $val) {
            $val->image_url = $this->filemanager->getFilePath($val->page);
        }
        // if ($input['page'] === 'back') {
        //     $result = array_reverse($result);
        // }
        $idcardhtml = $this->idcard_construct_pdf_html($result, $input['page'],$input['orientation']);
        echo json_encode($idcardhtml);
    }

    public function s3FileUpload($file ,$folder_name = 'profile')
    {
      if ($file['tmp_name'] == '' || $file['name'] == '') {
        return ['status' => 'empty', 'file_name' => ''];
      }
      return $this->filemanager->uploadFile($file['tmp_name'], $file['name'], $folder_name);
    }

    public function downloadGeneratedIdCardsPdf() {
        $imageUrls = $this->input->post('image_urls');
        $page_type = $this->input->post('page_type');
        $order_id = $this->input->post('order_id');
        $orientation = $this->input->post('orientation', true) ?: 'portrait';

        if (empty($imageUrls) || empty($order_id) || empty($page_type)) {
            echo json_encode(['success' => false, 'message' => 'Missing required parameters']);
            return;
        }

        // Define card dimensions based on orientation
        $cardWidth = ($orientation === 'portrait') ? '55mm' : '86mm';
        $cardHeight = ($orientation === 'portrait') ? '86mm' : '54mm';

        $html = '<!DOCTYPE html><html><head>';
        $html .= '<meta charset="UTF-8">';
        $html .= '<style>
            @page {
                margin: 1mm;
                size: A4 portrait;
            }
            body {
                margin: 0;
                padding: 0;
            }
            .card-container {
                width: 100%;
                padding:0;
                margin:0;
            }
            .card-row {
                display: block;
                width: 100%;
                clear: both;
                page-break-inside: avoid;
                break-inside:avoid;
            }
            .card-item {
                width: 335px;
                height: 215px;
                border: 1px solid #ccc;
                float: left;
                margin-right: 5mm;
                margin-left: 8mm;
                margin-top: 1.5mm;
                text-align: center;
                vertical-align: middle;
            }
            .card-item img {
                width: 335px;
                height: 215px;
            }
        </style>';
        $html .= '</head><body>';
        $html .= '<div class="card-container">';

        // Counter to manage two cards per row
        $counter = 0;

        foreach ($imageUrls as $imageData) {
            if ($counter % 2 == 0) {
                // New row every 2 images
                $html .= '<div class="card-row">';
            }
            $html .= '<div class="card-item">';
            $html .= '<img src="'.$imageData.'" alt="ID Card">';
            $html .= '</div>';

            $counter++;

            if ($counter % 2 == 0) {
                $html .= '</div>'; // Close the row
            }
        }

        // In case the last row was not closed
        if ($counter % 2 != 0) {
            $html .= '</div>';
        }

        $html .= '</div></body></html>';
        echo "<pre>"; print_r($html);die();
        // Save HTML to database
        $insert = $this->Idcards_model->updateIdcardpdfHtml($html, $order_id, $page_type);

        if ($insert) {
            // Generate PDF with the insert ID to update status when complete
            $path = $this->__generateidcardSelected_pdf($html, $insert['id']);

            // Wait for PDF generation to complete
            $maxRetries = 10;
            $retryCount = 0;
            $pdfStatus = 0;

            while ($retryCount < $maxRetries && $pdfStatus == 0) {
                sleep(1);
                $pdfStatus = $this->Idcards_model->check_pdf_status($insert['id']);
                $retryCount++;
            }

            if ($pdfStatus == 1) {
                $url = $this->filemanager->getFilePath($path);
                echo json_encode([
                    'success' => true,
                    'message' => 'PDF generated successfully',
                    'pdf_url' => $url
                ]);
            } else {
                // If PDF generation is taking too long, return the path anyway
                $url = $this->filemanager->getFilePath($path);
                echo json_encode([
                    'success' => true,
                    'message' => 'PDF is being generated',
                    'pdf_url' => $url
                ]);
            }
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to generate PDF']);
        }
    }




    public function construct_id_card_pdf_path(){
        $imageUrls = $this->input->post('image_urls');
        $html = '<!DOCTYPE html><html><head>';
        $html .= '<meta charset="UTF-8">';
        $html .= '<style>
            @page {
                margin: 2mm;
                size: A4 portrait;
            }
            body {
                margin: 0;
                padding: 0;
                font-family: Arial, sans-serif;
            }
            .card-container {
                width: 100%;
            }
            .card-row {
                display: block;
                width: 100%;
                clear: both;
                page-break-inside: avoid;
                margin-bottom: 10mm;
            }
            .card-item {
                width: 215px;
                height: 335px;
                border: 1px solid #ccc;
                float: left;
                margin-right: 10mm;
                margin-bottom: 10mm;
                text-align: center;
                vertical-align: middle;
            }
            .card-item img {
                width: 215px;
                height: 335px;
            }
        </style>';
        $html .= '</head><body>';
        $html .= '<div class="card-container">';

        // Counter to manage two cards per row
        $counter = 0;

        foreach ($imageUrls as $imageData) {
            if ($counter % 2 == 0) {
                // New row every 2 images
                $html .= '<div class="card-row">';
            }
            $html .= '<div class="card-item">';
            $html .= '<img src="'.$imageData.'" alt="ID Card">';
            $html .= '</div>';

            $counter++;

            if ($counter % 2 == 0) {
                $html .= '</div>'; // Close the row
            }
        }

        // In case the last row was not closed
        if ($counter % 2 != 0) {
            $html .= '</div>';
        }

        $html .= '</div></body></html>';
        //echo "<pre>"; print_r($html); die();
        // $insert = $this->Idcards_model->updateIdcardHtml($html,$order_id, $page_type);
        if($html){
            $path = $this->__generateidcardSelected_pdf($html);
            sleep(5);
        }
        $url = $this->filemanager->getFilePath($path);
        $data = file_get_contents($url);
        $this->load->helper('download');
        force_download('idcards.pdf', $data, TRUE);
    }

    private function __generateidcardSelected_pdf($html, $pdf_id = null) {
        $school = CONFIG_ENV['main_folder'];
        $path = $school.'/idcards/'.uniqid().'-'.time().".pdf";

        $bucket = $this->config->item('s3_bucket');

        // Update PDF path in database if ID is provided
        if ($pdf_id) {
            $this->db->where('id', $pdf_id);
            $this->db->update('idcard_pdf_generate', ['pdf_path' => $path]);
        }

        $page = 'portrait'; // Default orientation
        $page_size = 'a4';
        $curl = curl_init();
        $postData = $html;
        $username = CONFIG_ENV['job_server_username'];
        $password = CONFIG_ENV['job_server_password'];
        // Set return URL to update PDF status when generation is complete
        $return_url = site_url('Callback_Controller/updateGenertaedIdcardPathLink/' . ($pdf_id ? $pdf_id : '0'));

        curl_setopt_array($curl, array(
            CURLOPT_URL => CONFIG_ENV['job_server_pdfgen_uri'],
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_USERPWD => $username . ":" . $password,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => "path=".$path."&bucket=".$bucket."&page=".$page."&page_size=".$page_size."&data=".$postData."&return_url=".$return_url,
            CURLOPT_HTTPHEADER => array(
                "Accept: application/json",
                "Cache-Control: no-cache",
                "Content-Type: application/x-www-form-urlencoded",
                "Postman-Token: 090abdb9-b680-4492-b8b7-db81867b114e"
            ),
        ));

        $response = curl_exec($curl);
        $err = curl_error($curl);
        curl_close($curl);

        return $path;
    }

    public function class_teacher_id_card_approval(){
        $data['total_idcards_class_students'] = $this->Idcards_model->count_idcard_orders_by_status_for_class_students();
        $data['total_id_card_order_in_review_class_students'] = $this->Idcards_model->count_idcard_orders_by_status_for_class_students('in review');
        $data['total_id_card_order_in_approved_class_students'] = $this->Idcards_model->count_idcard_orders_by_status_for_class_students('approved');
        $data['total_id_card_order_in_removed_class_students'] = $this->Idcards_model->count_idcard_orders_by_status_for_class_students('removed');

        $data['main_content'] = 'idcard/class_teacher_id_card_approval_view';
        $this->load->view('inc/template',$data);
    }

    public function approve_idcards_by_class_teacher($order_id){
        $data['order_id'] = $order_id;
        $data['order_details'] = $this->Idcards_model->getOrderDetails($order_id);
        $template = $this->Idcards_model->get_template($data['order_details']->idcard_template_id);
        $data['template'] = $template->id;
        $data['staff_list'] = $this->Idcards_model->get_all_staffs();
        // echo "<pre>";print_r($data['staff_list']);die();

        // Get status counts for this order
        $data['status_counts'] = $this->Idcards_model->get_order_status_counts($order_id);
        $data['main_content'] = 'idcard/approve_idcards_by_class_teacher_view';
        $this->load->view('inc/template',$data);
    }

    public function get_class_teacher_wise_idcards() {
        $counts = $this->Idcards_model->get_class_teacher_wise_idcards();
        echo json_encode($counts);
    }

    public function update_order_status() {
        $order_id = $this->input->post('order_id');
        $status = $this->input->post('status');

        if (!$order_id || !$status) {
            echo json_encode(['success' => false, 'message' => 'Order ID and status are required']);
            return;
        }

        // Check if all entities are approved
        $status_counts = $this->Idcards_model->get_order_status_counts($order_id);

        if ($status_counts->total == 0 || $status_counts->approved < ($status_counts->total - ($status_counts->re_ordered + $status_counts->removed)) || $status_counts->in_review > 0) {
            echo json_encode(['success' => false, 'message' => 'All ID cards must be approved before sending for printing']);
            return;
        }

        // Update the order status
        $result = $this->Idcards_model->update_order_status($order_id, $status);

        if ($result) {
            echo json_encode(['success' => true, 'message' => 'Order status updated successfully']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to update order status']);
        }
    }

    /**
     * Clone an existing order for unapproved entities
     * Creates a new order with the same template for unapproved staff/students
     */
    public function clone_order() {
        // Get the order ID from the POST data
        $order_id = $this->input->post('order_id');

        if (!$order_id) {
            echo json_encode(['success' => false, 'message' => 'Order ID is required']);
            return;
        }

        // Call the model method to clone the order
        $result = $this->Idcards_model->clone_order_for_unapproved($order_id);

        if ($result && isset($result['new_order_id'])) {
            echo json_encode([
                'success' => true,
                'message' => 'Order cloned successfully for unapproved entities',
                'new_order_id' => $result['new_order_id']
            ]);
        } else {
            $message = isset($result['message']) ? $result['message'] : 'Failed to clone order';
            echo json_encode(['success' => false, 'message' => $message]);
        }
    }

    public function order_detail_approve_idcards_by_class_teacher($order_id){
        $data['order_id'] = $order_id;
        $data['order_details'] = $this->Idcards_model->getOrderDetails($order_id);
        $template = $this->Idcards_model->get_template($data['order_details']->idcard_template_id);
        $data['template'] = $template->id;
        $data['staff_list'] = $this->Idcards_model->get_all_staffs();

        $data['status_counts'] = $this->Idcards_model->get_order_status_counts($order_id);
        $data['main_content']='idcard/order_detail_approve_idcards_by_class_teacher_view';
		$this->load->view('inc/template',$data);
    }

    public function get_template_name() {
        $template_id = $this->input->post('template_id');

        if (!$template_id) {
            echo json_encode(['success' => false, 'message' => 'Template ID is required']);
            return;
        }

        // Get the template data
        $template = $this->Idcards_model->get_template($template_id);

        if (!$template) {
            echo json_encode(['success' => false, 'message' => 'Template not found']);
            return;
        }

        // Return just the template name
        echo json_encode([
            'success' => true,
            'template_name' => $template->name
        ]);
    }

    /**
     * Convert JSON design data to PNG image
     *
     * @param string $json_data JSON string containing design data
     * @return string Binary PNG image data
     */
    private function convertJsonToPng($json_data) {
        try {
            // Parse the JSON data
            $design_data = json_decode($json_data, true);
            if (!$design_data) {
                throw new Exception('Invalid JSON data');
            }

            // Default dimensions if not specified in the design
            $width = isset($design_data['styles']['width']) ? $design_data['styles']['width'] * 4 : 215;
            $height = isset($design_data['styles']['height']) ? $design_data['styles']['height'] * 4 : 335;

            // Create a new image with the specified dimensions
            $image = imagecreatetruecolor(215, 335);

            // Enable alpha blending
            imagealphablending($image, true);
            imagesavealpha($image, true);

            // Fill with white background (or transparent if needed)
            $white = imagecolorallocate($image, 255, 255, 255);
            imagefill($image, 0, 0, $white);

            // Process design elements if they exist
            if (isset($design_data['elements']) && is_array($design_data['elements'])) {
                foreach ($design_data['elements'] as $element) {
                    $this->renderElement($image, $element);
                }
            }

            // Apply any global styles from the design
            if (isset($design_data['styles'])) {
                $this->applyStyles($image, $design_data['styles']);
            }

            // Capture the PNG data
            ob_start();
            imagepng($image, null, 0); // Use highest quality (0 compression)
            $png_data = ob_get_clean();

            // Clean up
            imagedestroy($image);

            return $png_data;

        } catch (Exception $e) {
            // If there's an error, create a simple error image
            $error_image = imagecreatetruecolor(215, 335);
            $white = imagecolorallocate($error_image, 255, 255, 255);
            $red = imagecolorallocate($error_image, 255, 0, 0);
            imagefill($error_image, 0, 0, $white);

            // Add error text
            imagestring($error_image, 3, 10, 10, 'Error: ' . $e->getMessage(), $red);

            // Capture the PNG data
            ob_start();
            imagepng($error_image, null, 0);
            $png_data = ob_get_clean();

            // Clean up
            imagedestroy($error_image);

            return $png_data;
        }
    }

    /**
     * Render a single element on the image
     *
     * @param resource $image GD image resource
     * @param array $element Element data from JSON
     */
    private function renderElement($image, $element) {
        // Default position and size
        $x = isset($element['x']) ? $element['x'] : 0;
        $y = isset($element['y']) ? $element['y'] : 0;
        $width = isset($element['width']) ? $element['width'] : 50;
        $height = isset($element['height']) ? $element['height'] : 20;

        // Get element type
        $type = isset($element['type']) ? $element['type'] : 'text';

        switch ($type) {
            case 'text':
                $this->renderTextElement($image, $element, $x, $y, $width, $height);
                break;

            case 'image':
                $this->renderImageElement($image, $element, $x, $y, $width, $height);
                break;

            case 'shape':
                $this->renderShapeElement($image, $element, $x, $y, $width, $height);
                break;

            case 'field':
                $this->renderTextElement($image, $element, $x, $y, $width, $height);
                break;

            // Add more element types as needed
        }
    }

    /**
     * Render a text element on the image
     */
    private function renderTextElement($image, $element, $x, $y, $width, $height) {
        // Get text content
        if (isset($element['properties']) && isset($element['properties']['fieldName'])) {
            // This is a field element (placeholder)
            $text = $element['properties']['fieldName'];
        } else if (isset($element['text'])) {
            $text = $element['text'];
        } else {
            $text = '';
        }

        // Get text style properties
        if (isset($element['properties'])) {
            $fontSize = isset($element['properties']['size']) ? $element['properties']['size'] : 12;
            $fontColor = isset($element['properties']['color']) ? $element['properties']['color'] : '#000000';
            $bold = isset($element['properties']['bold']) && $element['properties']['bold'];
            $italic = isset($element['properties']['italic']) && $element['properties']['italic'];
            $textAlign = isset($element['properties']['textAlign']) ? $element['properties']['textAlign'] : 'left';
        } else if (isset($element['style'])) {
            $fontSize = isset($element['style']['fontSize']) ? $element['style']['fontSize'] : 12;
            $fontColor = isset($element['style']['color']) ? $element['style']['color'] : '#000000';
            $bold = false;
            $italic = false;
            $textAlign = 'left';
        } else {
            $fontSize = 12;
            $fontColor = '#000000';
            $bold = false;
            $italic = false;
            $textAlign = 'left';
        }

        // Convert hex color to RGB
        $rgb = $this->hexToRgb($fontColor);
        $color = imagecolorallocate($image, $rgb['r'], $rgb['g'], $rgb['b']);

        // Adjust text position based on alignment
        $textWidth = strlen($text) * $fontSize * 0.6; // Approximate text width

        if ($textAlign == 'center') {
            $x = $x + ($width - $textWidth) / 2;
        } else if ($textAlign == 'right') {
            $x = $x + $width - $textWidth;
        }

        // Simple text rendering - for more advanced text, you might need to use TrueType fonts
        // If we have a font file, we could use imagettftext for better rendering
        imagestring($image, $fontSize / 2, $x, $y, $text, $color);
    }

    /**
     * Render an image element on the image
     */
    private function renderImageElement($image, $element, $x, $y, $width, $height) {
        // Check if we have a source URL in properties
        if (isset($element['properties']) && isset($element['properties']['src']) && !empty($element['properties']['src'])) {
            $src = $element['properties']['src'];
        }
        // Check if we have a source URL directly in the element
        else if (isset($element['src']) && !empty($element['src'])) {
            $src = $element['src'];
        } else {
            return; // No image source found
        }

        // Handle base64 encoded images
        if (strpos($src, 'data:image') === 0) {
            // Extract the base64 data
            $base64_data = substr($src, strpos($src, ',') + 1);
            $img_data = base64_decode($base64_data);

            // Create image from string
            $src_img = imagecreatefromstring($img_data);
            if ($src_img) {
                // Check if auto-rotate is enabled
                $autoRotate = false;
                if (isset($element['properties']) && isset($element['properties']['autoRotate'])) {
                    $autoRotate = (bool)$element['properties']['autoRotate'];
                }

                // Get rotation angle if specified
                $rotation = 0;
                if (isset($element['properties']) && isset($element['properties']['rotation'])) {
                    $rotation = (int)$element['properties']['rotation'];
                }

                // Apply rotation if needed
                if ($rotation != 0 || $autoRotate) {
                    $src_img = imagerotate($src_img, -$rotation, 0);
                }

                // Copy and resize the image to the target location
                imagecopyresampled($image, $src_img, $x, $y, 0, 0, $width, $height, imagesx($src_img), imagesy($src_img));
                imagedestroy($src_img);
            }
        } else {
            // For URL-based images, we'd need to fetch them first
            // This is a simplified example - in production, you'd want to add caching
            $img_data = @file_get_contents($src);
            if ($img_data) {
                $src_img = imagecreatefromstring($img_data);
                if ($src_img) {
                    // Check if auto-rotate is enabled
                    $autoRotate = false;
                    if (isset($element['properties']) && isset($element['properties']['autoRotate'])) {
                        $autoRotate = (bool)$element['properties']['autoRotate'];
                    }

                    // Get rotation angle if specified
                    $rotation = 0;
                    if (isset($element['properties']) && isset($element['properties']['rotation'])) {
                        $rotation = (int)$element['properties']['rotation'];
                    }

                    // Apply rotation if needed
                    if ($rotation != 0 || $autoRotate) {
                        $src_img = imagerotate($src_img, -$rotation, 0);
                    }

                    imagecopyresampled($image, $src_img, $x, $y, 0, 0, $width, $height, imagesx($src_img), imagesy($src_img));
                    imagedestroy($src_img);
                }
            }
        }
    }

    /**
     * Render a shape element on the image
     */
    private function renderShapeElement($image, $element, $x, $y, $width, $height) {
        // Get shape type
        $shape = isset($element['shape']) ? $element['shape'] : 'rectangle';

        // Get style properties
        $fillColor = isset($element['style']['backgroundColor']) ? $element['style']['backgroundColor'] : '#FFFFFF';
        $borderColor = isset($element['style']['borderColor']) ? $element['style']['borderColor'] : '#000000';
        $borderWidth = isset($element['style']['borderWidth']) ? $element['style']['borderWidth'] : 1;

        // Convert hex colors to RGB
        $fillRgb = $this->hexToRgb($fillColor);
        $borderRgb = $this->hexToRgb($borderColor);

        $fill = imagecolorallocate($image, $fillRgb['r'], $fillRgb['g'], $fillRgb['b']);
        $border = imagecolorallocate($image, $borderRgb['r'], $borderRgb['g'], $borderRgb['b']);

        switch ($shape) {
            case 'rectangle':
                // Draw filled rectangle
                imagefilledrectangle($image, $x, $y, $x + $width, $y + $height, $fill);

                // Draw border if needed
                if ($borderWidth > 0) {
                    imagerectangle($image, $x, $y, $x + $width, $y + $height, $border);
                }
                break;

            case 'circle':
                // Calculate center and radius
                $centerX = $x + ($width / 2);
                $centerY = $y + ($height / 2);
                $radius = min($width, $height) / 2;

                // Draw filled circle
                imagefilledellipse($image, $centerX, $centerY, $radius * 2, $radius * 2, $fill);

                // Draw border if needed
                if ($borderWidth > 0) {
                    imageellipse($image, $centerX, $centerY, $radius * 2, $radius * 2, $border);
                }
                break;
        }
    }

    /**
     * Apply global styles to the image
     */
    private function applyStyles($image, $styles) {
        // Apply background color if specified
        if (isset($styles['backgroundColor']) && !empty($styles['backgroundColor'])) {
            $rgb = $this->hexToRgb($styles['backgroundColor']);
            $bgColor = imagecolorallocate($image, $rgb['r'], $rgb['g'], $rgb['b']);
            imagefill($image, 0, 0, $bgColor);
        }

        // Apply background image if specified
        if (isset($styles['backgroundImage']) && !empty($styles['backgroundImage'])) {
            // Extract URL from CSS background-image property
            $url = $styles['backgroundImage'];
            $url = str_replace('url(', '', $url);
            $url = str_replace(')', '', $url);
            $url = trim($url, '"\'' );

            if (!empty($url)) {
                // For base64 encoded images
                if (strpos($url, 'data:image') === 0) {
                    $base64_data = substr($url, strpos($url, ',') + 1);
                    $img_data = base64_decode($base64_data);
                    $bg_img = imagecreatefromstring($img_data);

                    if ($bg_img) {
                        // Copy background image to fill the entire canvas
                        imagecopyresampled($image, $bg_img, 0, 0, 0, 0, imagesx($image), imagesy($image), imagesx($bg_img), imagesy($bg_img));
                        imagedestroy($bg_img);
                    }
                } else {
                    // For URL-based images
                    $img_data = @file_get_contents($url);
                    if ($img_data) {
                        $bg_img = imagecreatefromstring($img_data);
                        if ($bg_img) {
                            imagecopyresampled($image, $bg_img, 0, 0, 0, 0, imagesx($image), imagesy($image), imagesx($bg_img), imagesy($bg_img));
                            imagedestroy($bg_img);
                        }
                    }
                }
            }
        }

        // Apply border if specified
        if (isset($styles['borderRadius']) && $styles['borderRadius'] > 0) {
            // Note: GD doesn't support rounded corners directly
            // For a production app, you might need a more complex solution
            // This is just a placeholder
        }
    }

    /**
     * Convert hex color to RGB array
     */
    private function hexToRgb($hex) {
        // Remove # if present
        $hex = ltrim($hex, '#');

        // Parse hex color
        if (strlen($hex) == 3) {
            $r = hexdec(substr($hex, 0, 1) . substr($hex, 0, 1));
            $g = hexdec(substr($hex, 1, 1) . substr($hex, 1, 1));
            $b = hexdec(substr($hex, 2, 1) . substr($hex, 2, 1));
        } else {
            $r = hexdec(substr($hex, 0, 2));
            $g = hexdec(substr($hex, 2, 2));
            $b = hexdec(substr($hex, 4, 2));
        }

        return ['r' => $r, 'g' => $g, 'b' => $b];
    }

     public function generate_qr_code_for_idcards(){
        $this->load->library('ciqrcode');
        
        $dataType = $this->input->get('dataType');
        if($dataType == 'bar_code'){
            $data = $this->input->get('qrdata');
            $size = $this->input->get('qr_size');

            // Load barcode library (e.g., Zend_Barcode or similar)
            $this->load->library('zend');
            $this->zend->load('Zend/Barcode');
            // Generate barcode as PNG and output as base64
            $barcodeOptions = [
                'text' => $data,
                'barHeight' =>$size ? intval($size) : 12,
                'drawText' => true, // This will remove the text below the barcode
                'width' => 100,
            ];
            
            $rendererOptions = [
                'imageType' => 'png',
                'horizontalPosition' => 'center',
                'verticalPosition' => 'middle',
            ];

            ob_start();
            Zend_Barcode::factory('code128', 'image', $barcodeOptions, $rendererOptions)->render();
            $barcodeBinary = ob_get_clean();

            $base64 = base64_encode($barcodeBinary);
            echo 'data:image/png;base64,' . $base64;
        }else{
            $data = $this->input->get('qrdata');
            $size = $this->input->get('qr_size');
            // Define QR code parameters
            $params = array(
                'data' => $data,
                'level' => 'H', // Error correction level: L, M, Q, H
                'size' => $size,    // Size of the QR code
                'savename' => null // No file save, output to buffer
            );
            ob_start();
            $this->ciqrcode->generate($params, false);
            $qrCodeBinary = ob_get_clean();

            // Encode the QR code as base64
            $base64 = base64_encode($qrCodeBinary);
            echo 'data:image/png;base64,' . $base64;
        }
       
    }

    function get_avatar_issued_idcards(){
        $result=$this->Idcards_model->get_avatar_issued_idcards();
        echo json_encode($result);
    }

}
?>