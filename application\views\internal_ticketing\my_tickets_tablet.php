<div class="card panel_new_style">
    <div class="card-header panel_heading_new_style_padding" style="padding-top: 10px;">
        <h3 class="card-title panel_title_new_style">
          <a class="back_anchor" href='<?php echo site_url("internal_ticketing"); ?>'>
            <span class="fa fa-arrow-left"></span>
          </a>
            <strong>My Tickets</strong>
            <a href="<?php echo site_url('internal_ticketing/create_ticket');?>"  class="new_circleShape_res" style="background-color: #fe970a;" >
              <span class="fa fa-plus" style="font-size: 19px;"></span>
            </a>
        </h3>
    </div>
</div>
<div class="card-body pt-0 pb-0">
  <div class="row" style="margin:0">
    <div class="col-md-3">
      <label>Saved Filters</label>
      <select name="" id="filter_types" class="form-control select" style="margin-top: -2px" onchange="get_ticket_data(is_start=true)">
        <option value="reported_by_me">My Tickets</option>
        <option value="all_open_tickets_reported_by_me">My Open Tickets</option>
        <option value="all_resolved_tickets_reported_by_me">My Resolved Tickets</option>
        <option value="all_closed_tickets_reported_by_me">My Closed Tickets</option>
      </select>
    </div>
  </div>
</div>
<hr style="display:block !important">
<div class="card-body pt-0 pb-0">
    <div class="row" style="margin: 0px">
        <div class="col-md-3">
            <label>Issue Type</label>
            <select class="form-control select"  name="issue_type" multiple="" title="All" id="issue_type">
                <?php foreach ($issue_type as $key => $val) { ?>
                    <option value="<?php echo $val->issue_id ?>" style="color:black;"><?php echo $val->issue_name ?></option>
                <?php } ?>
            </select>
        </div>

        <div class="col-lg-3 mt-2">
          <label>Priority</label>
          <select class="form-control select" multiple="" title="All"  name="priority" id="priority">
            <?php foreach ($priority as $key => $val) { ?>
              <option value="<?php echo $val->priority_id ?>"><?php echo $val->priority_name ?></option>
            <?php } ?>
          </select>
        </div>

        <div class="col-lg-3 mt-2">
          <label>Status</label>
          <select class="form-control select" multiple="" title="All"  name="status" id="status">
              <option value="Open">Open</option>
              <option value="In Progress">In Progress</option>
              <option value="Resolved">Resolved</option>
              <option value="Closed">Closed</option>
              <option value="Reopen">Reopened</option>
          </select>
        </div>

        <div class="col-sm-3 col-md-3">
          <center>
            <button id="generate" class="btn btn-primary" onclick="get_report()" >Get Report</button>
          </center>
        </div>
      </div>
    </div>

    <hr style="display:block !important">

    <div class="card-body">
      <div id="loader" class="loaderclass" style="display:none;">
    </div>
    <div class="col-md-6" style="padding: 0;">
      <div class="list-group-ticket">
      </div>
    </div>
    <div class="col-md-6" style="padding: 0;">
      <div class="card panel_new_style">
        <div class="card-header panel_heading_new_style_padding" style="padding-top: 10px;">
          <h3 class="card-title panel_title_new_style">
            <strong>Ticket Details</strong>
          </h3>
        </div>
        <div class="card-body">
          <div id="ticketDetails">
            <h5 style="padding:15px; font-size:18px;">Select a ticket to view details</h5>
          </div>
        </div>
      </div>
    </div>
    </div>
</div>

<style type="text/css">
.loaderclass {
  border: 8px solid #eee;
  border-top: 8px solid #7193be;
  border-radius: 50%;
  width: 48px;
  height: 48px;
  position: fixed;
  z-index: 1;
  animation: spin 2s linear infinite;
  margin-top: 35%;
  margin-left: 40%;
  position: absolute;
  z-index: 99999;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.active{
  background: #6893ca;
}

.disabled{
  background: #41474e57;
}

.danger{
  background: #FF0000;
}

.list-group-item {
  cursor: pointer;
}

.list-group-item:hover {
  background-color: #f8f9fa;
}

.list-group-item.active {
  background-color: #007bff;
  color: white;
}
</style>

<script>
  $(document).ready(function(){
      $('#dataTable').DataTable({
          "ordering":false,
          "language": {
            "searchPlaceholder": "Enter Search..."
          },
          "lengthMenu": [ [10, 25, 50, -1], [10, 25, 50, "All"] ],
              "pageLength": 10,
      });
      get_ticket_data(is_start=true);
  });

  function get_report(){
      $('#generate').prop('disabled',true).html('Please wait..');
      get_ticket_data(is_start=true);
      $('#generate').prop('disabled',false).html('Get Report');
  }

  function get_ticket_data(is_start=true) {
    var issue_type = $('#issue_type').val();
    if (issue_type == null) {
      issue_type = '';
    }
    var priority = $('#priority').val();
    if (priority == null) {
      priority = '';
    }
    var status = $('#status').val();
    if (status == null) {
      status = '';
    }
    
    // Always filter by reported_by_me for My Tickets
    var reported_by_me = 'on';
    var assigned_to_me = '';
    var filter_type = $('#filter_types').val();

    $.ajax({
      url: '<?php echo site_url('internal_ticketing/get_ticket_data'); ?>',
      data: {'issue_type': issue_type,'priority':priority, 'department': '', 'status': status, 'reported_by_me': reported_by_me, 'assigned_to_me': assigned_to_me, 'filter_type': filter_type},
      type: "post",
      success: function (data) {
        var data = JSON.parse(data);
        if (data =='') {
          $('.list-group-ticket').html('<h5 style="padding:15px; font-size:18px;">No Tickets available for selected filter</h5>');
          $('#ticketDetails').html('<h5 style="padding:15px; font-size:18px;">Select a ticket to view details</h5>');
          return false;
        }else{
        $('.list-group-ticket').html(construct_ticket_data(data));
        if (is_start && data.length > 0){
          // Auto-select first ticket
          onlclickgetId(data[0].ticketid);
        }
      }
      },
      error: function (err) {
        console.log(err);
      }
    });
  }

  function construct_ticket_data(tickets){
    var ticketArr = [];
    for(var e = 0; e < tickets.length; e++){
      ticketArr.push(tickets[e]);  
    }
    return construct_ticket_data1(ticketArr);
  }

  function construct_ticket_data1(ticket_data) {
    var output='';
    var m=1;
    
    for(var i = 0; i < ticket_data.length; i++){
      var bg = '';
      var className = '';
      var mr ='';
      if (m == 1) {
          var active = 'active';
        }else{
          var active = '';
        }
        output +=  `
        <a href="#" class="list-group-item ${active}" id="${ticket_data[i].ticketid}" onclick="onlclickgetId(${ticket_data[i].ticketid})">
          <div class="row">
            <div class="col-md-8">
              <h5><b>${ticket_data[i].ticket_item_id}</b></h5>
              <p class="mb-1">${ticket_data[i].title}</p>
              <small>Created: ${ticket_data[i].created_on}</small>
            </div>
            <div class="col-md-4 text-right">
              <span class="badge ${getStatusBadgeClass(ticket_data[i].ticket_status)}">${ticket_data[i].ticket_status}</span>
              <br><small class="text-muted">${ticket_data[i].priority_name}</small>
            </div>
          </div>
        </a>`;

      m++;
    }
    
    return '<div class="list-group">' + output + '</div>';
  }

  function getStatusBadgeClass(status) {
    switch(status) {
      case 'Open':
        return 'badge-primary';
      case 'In Progress':
        return 'badge-warning';
      case 'Resolved':
        return 'badge-success';
      case 'Closed':
        return 'badge-secondary';
      case 'Reopen':
        return 'badge-info';
      default:
        return 'badge-light';
    }
  }

  function onlclickgetId(ticket_id) {
    $('.list-group-item').removeClass('active');
    $('#'+ticket_id).addClass('active');
    get_ticket_details(ticket_id);
  }

  function get_ticket_details(ticket_id) {
    if (ticket_id == '') {
      $('#ticketDetails').html('<h5 style="padding:15px; font-size:18px;">Select a ticket to view details</h5>');
      return false;
    }
    $('#loader').show();
    $.ajax({
      url: '<?php echo site_url('internal_ticketing/ticket_details_by_id'); ?>',
      data: {'ticket_id': ticket_id},
      type: "post",
      success: function (data) {
        $('#loader').hide();
        var data = JSON.parse(data);
        $('#ticketDetails').html(construct_ticket_template(data));
      },
      error: function (err) {
        $('#loader').hide();
        console.log(err);
      }
    });
  }

  function construct_ticket_template(ticket_data) {
    var resolution_remarks = '';
    if (ticket_data.resolution_remarks == null){
      resolution_remarks = '-';
    }else{
      resolution_remarks = ticket_data.resolution_remarks;
    }
    
    var std = '';
    std += '<table class="table table-bordered">';
    std += '<tr><td style="width:150px;"><strong>Ticket Number</strong></td><td>'+ticket_data.ticket_item_id+'</td></tr>';
    std += '<tr><td><strong>Title</strong></td><td>'+ticket_data.title+'</td></tr>';
    std += '<tr><td><strong>Created Date</strong></td><td>'+ticket_data.created_on+'</td></tr>';
    std += '<tr><td><strong>Description</strong></td><td>'+ticket_data.description+'</td></tr>';
    std += '<tr><td><strong>Priority</strong></td><td>'+ticket_data.priority_name+'</td></tr>';
    std += '<tr><td><strong>Status</strong></td><td><span class="badge '+getStatusBadgeClass(ticket_data.ticket_status)+'">'+ticket_data.ticket_status+'</span></td></tr>';
    std += '<tr><td><strong>Issue Type</strong></td><td>'+ticket_data.issue_name+'</td></tr>';
    std += '<tr><td><strong>Assigned To</strong></td><td>'+ticket_data.assigned_to_staff_name+'</td></tr>';
    std += '<tr><td><strong>Department</strong></td><td>'+ticket_data.department_name+'</td></tr>';
    if (resolution_remarks != '-') {
      std += '<tr><td><strong>Resolution</strong></td><td>'+resolution_remarks+'</td></tr>';
    }
    std += '</table>';
    
    return std;
  }
</script>
