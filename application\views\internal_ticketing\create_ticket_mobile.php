<!-- START BREADCRUMB -->
<div class="card panel_new_style">
    <div class="card-header panel_heading_new_style_padding" style="padding-top: 10px;">
        <h3 class="card-title panel_title_new_style">
          <a class="back_anchor" href='<?php echo site_url("internal_ticketing"); ?>'>
            <span class="fa fa-arrow-left"></span>
          </a>
            <strong>Create Ticket</strong>
        </h3>
    </div>
</div>
<div class="card-body px-2 py-1">
<!-- start -->
<div style="margin-top:5px; overflow: auto;">
      <form method="post" class="form-horizontal" id="internalTicket" data-parsley-validate="">
        <input type="hidden" name="created_by" id="created_by" value="<?php echo $this->authorization->getAvatarStakeHolderId(); ?>">
        <div class="col-md-8 col-md-offset-1">
          <div class="panel-body">

          <div class="form-group">
            <label class="control-label" for="it_title"  style="text-align:left">Title <font color="red">*</font></label>
            <!-- <div class="input-group col-md-6"> -->
                <input type="text" class="form-control" id="title" placeholder="Enter Title" name="it_title" data-parsley-error-message="Cannot be empty" required>
            <!-- </div> -->
            <div class="col-sm-8">          
            </div>
          </div>

          <div class="form-group">
            <label class="control-label" for="reported_by">Reported By</label>
            <?php if ($this->authorization->isAuthorized('INTERNAL_TICKETING.CREATE_TICKET_ON_BEHALF')) { ?>
              <select class="form-control select" style="display: none;"  id="reported_by" name="reported_by">
                <?php 
                  foreach($staff_list as $it => $cl){ ?>
                    <option value="<?php echo $cl->staff_id ?>" <?php echo ($cl->staff_id == $logged_in_staff) ? 'selected' : ''; ?>>
                        <?php echo $cl->staff_name ?>
                    </option>
                    <!-- echo "<option value='$cl->staff_id'>$cl->staff_name</option>"; -->
                  <?php }
                ?>
              </select>
            <?php } else{?>
              <select class="form-control select" disabled style="background-color: #adb3b9; color: #666a6e;"  id="reported_by" name="reported_by">
                <?php 
                  foreach($staff_list as $it => $cl){ ?>
                    <option value="<?php echo $cl->staff_id ?>" <?php echo ($cl->staff_id == $logged_in_staff) ? 'selected' : ''; ?>>
                        <?php echo $cl->staff_name ?>
                    </option>
                  <?php }
                ?>
              </select>
            <?php } ?>
          </div>

          <?php if ($this->settings->getSetting('enable_confidential_option_in_internal_ticketing')){?>
            <div class="form-group">
              <label class="control-label" for="confidentiality">Confidential <font color="red">*</font></label>
              <select class="form-control select" required="" data-parsley-error-message="Cannot be empty" id="confidentiality" name="confidentiality">
                <option value="0">No</option>
                <option value="1">Yes</option>
              </select>
              <span class="help-block">If Confidential is YES, ticket will be visible to only You and the Resolver.</span>
            </div>
          <?php } ?>

          <div class="form-group">
            <label class="control-label" for="issue_type" style="text-align: left">Issue Type <font color="red">*</font></label>
            <!-- <div class="input-group col-md-9"> -->
                <select class="form-control select2" required="" data-parsley-error-message="Cannot be empty" id="issue_type" name="issue_type" onchange="getAssignedStaff()">
                    <option value="">Select Issue</option>
                      <?php 
                        foreach($issue_type as $it => $cl){
                          echo "<option value='$cl->issue_id'>$cl->issue_name</option>";
                        }
                      ?>
                </select>
            <!-- </div> -->
          </div>

          <div class="form-group" id="assigned_to_div">
          </div>
          
          <div class="form-group">
            <label class="control-label" for="priority" style="text-align: left">Priority <font color="red">*</font></label>
            <!-- <div class="input-group col-md-9"> -->
                <select class="form-control select2" required="" data-parsley-error-message="Cannot be empty" id="priority" onchange="getPriorityDesc()" name="priority">
                    <option value="">Select Priority</option>
                    <?php 
                      foreach($priority as $pt => $cl){
                        echo "<option value='$cl->priority_id'>$cl->priority_name</option>";
                      }
                  ?>
                </select>
            <!-- </div> -->
          </div>

          <div class="form-group" id="priority_description_div">
          </div>

          <div class="form-group">
            <label class="control-label" for="description" style="text-align: left">Description</label>
            <!-- <div class="input-group col-md-9"> -->
              <textarea class="form-control"  id="description" rows="3" name="it_description" placeholder="Enter Description"></textarea>
            <!-- </div> -->
          </div>

          <div class="form-group">
              <input hidden="" type="file" name="it_documents" id="it_documents" accept="image/*, application/pdf, text/plain, video/mp4" multiple="">
              <label class="control-label" for="description" style="text-align: left">Attach File(s)</label>
              <div class="input-group col-md-9">           
                <label  class="btn btn-primary add-file" for="it_documents"><i class="fa fa-files-o"></i>&nbsp;&nbsp;&nbsp;Add File(s)</label>
                <span class="help-block" style="margin-left:10px">Allowed file types - jpeg, jpg, png, mp4 and pdf. Allowed size - upto 10Mb</span>
                <span id="fileuploadError" style="color: red;"></span>
              </div>
          </div>

          </div>
          <div class="form-group">
            <table class="table table-bordered col-md-11" id="file-list" style="display: none; margin-left:3%">
                <thead>
                  <tr>
                    <th>Filename</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody id="file-table">
                    <!-- Files will be listed here -->
                </tbody>
            </table>
          </div>
        </div>
        <div class="col-md-12">
        <div class="progress-bar" id="single-file-percentage" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
          <center>
            <button type="button" style="width: 9rem; border-radius: .45rem;margin-top: 2%;" id="save-file" class="btn btn-primary" onclick="saveFilesMobile()">Submit</button>
            <a class="btn btn-danger" style="width: 9rem; border-radius: .45rem;margin-top: 2%;" href="<?php echo site_url('internal_ticketing'); ?>">Cancel/Back</a>
          </center>
        </div>
      </form>

    </div>
  </div>
</div>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script type="text/javascript">
  $('#submitBack').click(function() {
    var $form = $('#staffTicket');
      if ($form.parsley().validate()){
        $("#submitBack").prop('disabled', 'disabled').val('Please wait...');
        $('#staffTicket').submit(); 
      }
  });

  $('#submitStay').click(function() {
    var $form = $('#staffTicket');
      if ($form.parsley().validate()){
        $("#submitStay").prop('disabled', 'disabled').val('Please wait...');
        $('#staffTicket').submit(); 
        window.location.reload();
      }
  });

  function getAssignedStaff(){
    var issue_type = $('#issue_type').val();
    var html = '';
    $.ajax({
      url: '<?php echo site_url('internal_ticketing/get_assigned_staff'); ?>',
      type: 'post',
      data: {'issue_type':issue_type},
      success: function(data) {
          var data = JSON.parse(data);
          html += '';
          html += `
            <label class="control-label" style="text-align:left">Assigned To </label>
            <input type="text" class="form-control" id="assigned_to" name="assigned_to" readonly="" value="${data.assigned_staff}">
            <div class="col-sm-8">          
            </div>
          </div>`;
          $("#assigned_to_div").html(html);
      }
    });
  }

  function getPriorityDesc(){
    var priority = $('#priority').val();
    var html = '';
    $.ajax({
      url: '<?php echo site_url('internal_ticketing/get_priority_desc'); ?>',
      type: 'post',
      data: {'priority':priority},
      success: function(data) {
          var data = JSON.parse(data);
          html += '';
          html += `
            <label class="control-label" style="text-align:left">Priority Description </label>
            <textarea class="form-control" rows="3" name="priority_desc" id="priority_desc" readonly=""></textarea>
            <div class="col-sm-8">          
            </div>
          </div>`;
          $("#priority_description_div").html(html);
          $('#priority_desc').html(data.priority_description);
      }
    });
  }
  
  
  selectedFiles = [];
  function after_file_validation(){
    var files = $("#it_documents").get(0).files;
    var html = '';
    var j = 1;
    for(var i in files) {
        if(typeof(files[i]) != 'object') {
            continue;
        }
        if(alreadySelected(files[i].name)) {
            $(function(){
                new PNotify({
                    title: 'Warning',
                    text: 'File already added',
                    type: 'warning',
                });
            });
            continue;
        }
        html += '<tr id="file_'+(j)+'">';
        selectedFiles.push(files[i]);
        html += '<td style="word-break: break-all">'+files[i].name+'</td>';
        html += '<td style="width: 10%;"><button style="width: 80px;" class="btn btn-sm btn-danger" onclick="removeFile('+j+',\''+files[i].name+'\')">Remove</button></td>';
        html += '</tr>';
        j++;
    }
    $("#file-table").append(html);
    $('#file-list').css('display', 'table');
  }

  function removeFile(index, file_name) {
      for(var i in selectedFiles) {
          if(file_name == selectedFiles[i].name) {
              selectedFiles.splice(i, 1);
              var k = parseInt(i)+1;
              break;
          }
      }
      
      $("#file_"+(index)).remove();
      if(selectedFiles.length == 0) {
        $('#file-list').css('display', 'none');
      }
  }

  function alreadySelected(file_name) {
      for(var i in selectedFiles) {
          if(file_name == selectedFiles[i].name) {
              return 1;
          }
      }
      return 0;
  }

  var completed_promises = 0;
  var total_promises = 0;
  var in_progress_promises = 0;
  var current_percentage = 0;
  function saveFiles() {
      completed_promises = 0;
      total_promises = 0;
      current_percentage = 0;
      in_progress_promises = 0;
      var title = $("#title").val();
      var issue_type = $("#issue_type").val();
      var priority = $("#priority").val();
      var formm = $('#internalTicket');
      if (formm.parsley().validate()){
        if (title == ''){
          $('#title').attr('required','required');
          return false;
        }else{
            $('#title').removeAttr('required');
        }
        if (issue_type == ''){
          $('#issue_type').attr('required','required');
          return false;
        }else{
            $('#issue_type').removeAttr('required');
        }
        if (priority == ''){
          $('#priority').attr('required','required');
          return false;
        }else{
            $('#priority').removeAttr('required');
        }
        single_file_progress();
        // if(selectedFiles.length == 0) {
        //     return false;
        // }
        var task_id = $("#submission_task_id").val();
        bootbox.confirm({
          title: "Submit Ticket?",
          style: 'width=50%',
          message: "<span><center>Once submitted, you cannot make any changes to the ticket. Are you sure you want to proceed?</center></span>",
          className: "widthadjust",
          width: '50%',
          buttons: {
            confirm: {
              label: 'Yes',
              className: 'btn-success btn-width'
            },
            cancel: {
              label: 'No',
              className: 'btn-danger btn-width'
            }
          },
          callback: function (result) {
            if(result) {
              $("#save-file").attr('disabled', true).html('<i class="fa fa-spin fa-spinner"></i>');
              var promises = [];
              completed_promises = 0;
              total_promises = selectedFiles.length;
              in_progress_promises = total_promises;
              if (total_promises != 0){
                for(var i in selectedFiles) {
                    var promise = saveFileToStorage(selectedFiles[i]);
                    promises.push(promise);
                }
                Promise.all(promises).then((values) => {
                    $("#save-file").attr('disabled', false).html('Submit');
                    saveFilePaths(values);
                }).catch((err) => {
                    $("#save-file").attr('disabled', false).html('Retry');
                });
              }

              else{
                var empty_array = [];
                saveFilePaths(empty_array);
              }
            }
          }
        });
      }
  }

  function saveFilesMobile() {
      completed_promises = 0;
      total_promises = 0;
      current_percentage = 0;
      in_progress_promises = 0;
      var title = $("#title").val();
      var issue_type = $("#issue_type").val();
      var priority = $("#priority").val();
      var formm = $('#internalTicket');
      if (formm.parsley().validate()){
        if (title == ''){
          $('#title').attr('required','required');
          return false;
        }else{
            $('#title').removeAttr('required');
        }
        if (issue_type == ''){
          $('#issue_type').attr('required','required');
          return false;
        }else{
            $('#issue_type').removeAttr('required');
        }
        if (priority == ''){
          $('#priority').attr('required','required');
          return false;
        }else{
            $('#priority').removeAttr('required');
        }
        single_file_progress();
        // if(selectedFiles.length == 0) {
        //     return false;
        // }
        var task_id = $("#submission_task_id").val();
        bootbox.confirm({
          title: "Submit Ticket?",
          style: 'width=50%',
          message: "<span><center>Once submitted, you cannot make any changes to the ticket. Are you sure you want to proceed?</center></span>",
          className: "widthadjustmobile",
          width: '50%',
          buttons: {
            confirm: {
              label: 'Yes',
              className: 'btn-success btn-width'
            },
            cancel: {
              label: 'No',
              className: 'btn-danger btn-width'
            }
          },
          callback: function (result) {
            if(result) {
              $("#save-file").attr('disabled', true).html('<i class="fa fa-spin fa-spinner"></i>');
              var promises = [];
              completed_promises = 0;
              total_promises = selectedFiles.length;
              in_progress_promises = total_promises;
              if (total_promises != 0){
                for(var i in selectedFiles) {
                    var promise = saveFileToStorage(selectedFiles[i]);
                    promises.push(promise);
                }
                Promise.all(promises).then((values) => {
                    $("#save-file").attr('disabled', false).html('Submit');
                    saveFilePaths(values);
                }).catch((err) => {
                    $("#save-file").attr('disabled', false).html('Retry');
                });
              }

              else{
                var empty_array = [];
                saveFilePaths(empty_array);
              }
            }
          }
        });
      }
  }

  function complete_submission() {
      $("#saving-files-status").modal('hide');
      $("#save-status").html('');
      var progress = document.getElementById('single-file-percentage');
      progress.style.width = '0%';
  }

  function saveFilePaths(file_paths) {
    var title = $("#title").val();
    var issue_type = $("#issue_type").val();
    var priority = $("#priority").val();
    var description = $("#description").val();
    var created_by = $("#created_by").val();
    var reported_by = $("#reported_by").val();
    var reported_by_name = $("#reported_by option:selected").text();
    var confidentiality = $("#confidentiality").val();

    $.ajax({
      url: '<?php echo site_url('internal_ticketing/submit_ticket_data'); ?>',
      type: 'post',
      data: {title: title, issue_type:issue_type, paths:file_paths, priority:priority, description: description, created_by: created_by, reported_by: reported_by, reported_by_name: reported_by_name, confidentiality: confidentiality},
      success: function(data) {
          Swal.fire({
              // position: "top-end",
              icon: "success",
              title: "Ticket Created",
              showConfirmButton: false,
              timer: 4000
            });
          $(function(){
              // new PNotify({
              //     title: 'Success',
              //     text: 'Ticket Created',
              //     type: 'success',
              // });
          window.location.href = '<?php echo site_url('internal_ticketing') ?>';
          });
      },
      error: function (err) {
          console.log(err);
      }
    });
  }

  function single_file_progress(percentage) {
      if(percentage == 100) {
          in_progress_promises--;
          if(in_progress_promises == 0) {
              current_percentage = percentage;
          }
      } else {
          if(current_percentage<percentage) {
              current_percentage = percentage;
          }
      }
      var progress = document.getElementById('single-file-percentage');
      progress.style.width = current_percentage+'%';
      return false;
  }

  function saveFileToStorage(file) {
    return new Promise(function(resolve, reject) {
        try {
            $.ajax({
                url: '<?php echo site_url("S3_controller/getSignedUrl"); ?>',
                type: 'post',
                data: {'filename':file.name, 'file_type':file.type, 'folder':'internal_ticketing'},
                success: function(response) {
                    response = JSON.parse(response);
                    var path = response.path;
                    var signedUrl = response.signedUrl;

                    $.ajax({
                        url: signedUrl,
                        type: 'PUT',
                        headers: {
                            "Content-Type": file.type, 
                            "x-amz-acl":"public-read" 
                        },
                        processData: false,
                        data: file,
                        xhr: function () {
                            var xhr = $.ajaxSettings.xhr();
                            xhr.upload.onprogress = function (e) {
                                if (e.lengthComputable) {
                                    single_file_progress(e.loaded / e.total *100|0);
                                }
                            };
                            return xhr;
                        },
                        success: function(response) {
                            resolve({path:path, name:file.name, type:file.type});
                            // increaseLoading();
                        },
                        error: function(err) {
                            reject(err);
                        }
                    });
                },
                error: function (err) {
                    reject(err);
                }
            });
        } catch(err) {
            reject(err);
        }
    });
  }

  $('#it_documents').change(function(){
    var src = $(this).val();
    if(src && validateFile(this.files[0], 'fileuploadError')){
        $("#fileuploadError").html("");
    } else{
        this.value = null;
    }
  });

  function validateFile(file,errorId){
    if (file.size > 10000000 || file.fileSize > 10000000)
    {
       $("#"+errorId).html("Allowed file size exceeded. (Max. 10 MB)")
       return false;
    }
    if(file.type != 'image/jpeg' && file.type != 'image/jpg' && file.type != 'image/png' && file.type != 'application/pdf' && file.type != 'video/mp4') {
        $("#"+errorId).html("Allowed file types are jpeg, jpg, png, pdf, mp4");
        return false;
    }
    // return true;
    after_file_validation();
  }

</script>

<style>
  .widthadjust{
		width:600px;
		margin:auto;
	}
    .widthadjustmobile{
		width:300px;
		margin:auto;
	}
</style>

