<?php

class Event extends CI_Controller 
{
	function __construct()
	{
		parent::__construct();
		if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    } 
    if (!$this->authorization->isModuleEnabled('EVENT') && !$this->authorization->isAuthorized('EVENT.MODULE')) {
      redirect('dashboard', 'refresh');
    }
	  $this->load->model('event_model');
    $this->load->model('avatar');
    $this->load->helper('texting_helper');
    $this->load->library('filemanager');
    $this->load->model('student/Student_Model');
    $this->load->model('class_section');
    $this->load->model('student_wallet_model');
	}


	public function index(){
    $data['create_event'] = $this->authorization->isAuthorized('EVENT.CREATE_EVENT');
    $data['view_event_details'] = $this->authorization->isAuthorized('EVENT.VIEW_EVENT_DETAILS');
    $data['return_safety_deposit'] = $this->authorization->isAuthorized('EVENT.RETURN_SAFETY_DEPOSIT');
    $data['events'] = $this->event_model->get_event_details();
    $data['event_inveted'] = $this->event_model->event_invented_countSectionWise();
    // echo "<pre>"; print_r($data['events']); die();
    $data['main_content']    = 'event/index';
    $this->load->view('inc/template', $data);      
  }

  public function event_details($event_id){
    $data['create_event'] = $this->authorization->isAuthorized('EVENT.CREATE_EVENT');
    $data['view_event_details'] = $this->authorization->isAuthorized('EVENT.VIEW_EVENT_DETAILS');
    $data['return_safety_deposit'] = $this->authorization->isAuthorized('EVENT.RETURN_SAFETY_DEPOSIT');
    $data['event'] = $this->event_model->get_event_namebyid($event_id);
    $site_url = site_url();
    $data['more_tiles'] = array(
      [
        'title' => 'Edit Event',
        'sub_title' => 'Edit Event data',
        'icon' => 'svg_icons/events.svg',
        'url' => $site_url.'event/edit/'.$event_id,
        'permission' => $data['create_event'],
      ],

      [
        'title' => 'Manage Sub-Events',
        'sub_title' => 'Add '.$data['event']->sub_event_header,
        'icon' => 'svg_icons/events.svg',
        'url' => $site_url.'event/add_sub_event/'.$event_id,
        'permission' => $data['create_event'],
      ],
      [
        'title' => 'Student Registration',
        'sub_title' => 'Student Register',
        'icon' => 'svg_icons/events.svg',
        'url' => $site_url.'event/register_student/'.$event_id,
        'permission' => $data['view_event_details']
      ],

      [
        'title' => 'Send Notification',
        'sub_title' => 'Notification send',
        'icon' => 'svg_icons/events.svg',
        'url' => '#',
        'permission' =>$data['view_event_details']
      ],
      [
        'title' => 'Take Attendance',
        'sub_title' => 'Take attendance for event',
        'icon' => 'svg_icons/attendance.svg',
        'url' => $site_url.'event/take_attendance_event_list',
        'permission' => $data['view_event_details']
      ],
      [
        'title' => 'Discard Student',
        'sub_title' => 'Discard',
        'icon' => 'svg_icons/attendance.svg',
        'url' => $site_url.'event/discard_student_per_event/'.$event_id,
        'permission' => $data['view_event_details']
      ]    
    );
    $data['more_tiles'] = checkTilePermissions($data['more_tiles']);

    $data['more_event_reports'] = array(
      [
        'title' => 'Event registration report',
        'sub_title' => 'Event registration events',
        'icon' => 'svg_icons/events.svg',
        'url' => $site_url.'event/event_registration_report/'.$event_id,
        'permission' => $data['view_event_details']
      ],
      [
        'title' => 'Sub Event registration report',
        'sub_title' => 'sub Event registration events',
        'icon' => 'svg_icons/events.svg',
        'url' => $site_url.'event/sub_event_registration_report/'.$event_id,
        'permission' => $data['view_event_details']
      ],
      [
        'title' => 'Return Safety Deposit',
        'sub_title' => 'Return Safety Deposit',
        'icon' => 'svg_icons/attendance.svg',
        'url' => $site_url.'event/event_return_safety_deposit/'.$event_id,
        'permission' => $data['return_safety_deposit']
      ]
    );
    $data['more_event_reports'] = checkTilePermissions($data['more_event_reports']);

   
    $data['main_content']    = 'event/more_detail';
    $this->load->view('inc/template', $data);     
  }

  public function publish_event_by_id(){
    $stngId = $_POST['stngId'];
    $value = $_POST['value'];
    echo $this->event_model->publish_status_update_event_by_id($stngId,$value); 
  }

  public function event_dashboard(){
    $data['create_event'] = $this->authorization->isAuthorized('EVENT.CREATE_EVENT');
    $data['view_event_details'] = $this->authorization->isAuthorized('EVENT.VIEW_EVENT_DETAILS');
    $data['return_safety_deposit'] = $this->authorization->isAuthorized('EVENT.RETURN_SAFETY_DEPOSIT');

    $data['events'] = $this->event_model->get_event_details();

    $site_url = site_url();
    $data['tiles'] = array(
      [
        'title' => 'Manage Events',
        'sub_title' => 'Create and manage events',
	      'icon' => 'svg_icons/events.svg',
        'url' => $site_url.'event',
        'permission' => $this->authorization->isModuleEnabled('EVENT')
      ],
     

      // [
      //   'title' => 'Event registration',
      //   'sub_title' => 'Student Regsiter',
      //   'icon' => 'svg_icons/events.svg',
      //   'url' => $site_url.'event/register_student',
      //   'permission' => $data['event_permit']
      // ],

      // [
      //   'title' => 'Take Attendance',
      //   'sub_title' => 'Take attendance for event',
	     //  'icon' => 'svg_icons/attendance.svg',
      //   'url' => $site_url.'event/take_attendance_event_list',
      //   'permission' => $data['event_permit_entry']
      // ]
     
    );
    $data['tiles'] = checkTilePermissions($data['tiles']);

    $data['event_reports'] = array(
      [
        'title' => 'Event Summary Report ',
        'sub_title' => 'Event Summary Report ',
        'icon' => 'svg_icons/events.svg',
        'url' => $site_url.'event/summary_event',
        'permission' => $data['view_event_details']
      ],
    );
    $data['event_reports'] = checkTilePermissions($data['event_reports']);
    $data['main_content']    = 'event/menu';
    $this->load->view('inc/template', $data);      
  }

  public function take_attendance_event_list(){
    $data['events'] = $this->event_model->get_published_events_list();
    // echo "<pre>"; print_r($data['events']); die();
    $data['main_content']    = 'event/event_list';
    $this->load->view('inc/template', $data);    
  }
  public function qr_code_scan($evnId){
    $data['eventId'] = $evnId;
    $data['main_content']    = 'event/qr_code_scan';
    $this->load->view('inc/template', $data);     
  }

  public function add(){
    $data['event_code'] = $this->event_model->getLastInserted();
    // $data['classes'] = $this->event_model->get_classes_event();
    $data['sections'] = $this->event_model->get_all_section_list();
    $data['main_content']    = 'event/event_add';
    $this->load->view('inc/template', $data);
  }

  public function edit($evnId){
    $data['event_id'] = $evnId;
    $data['event_edit'] = $this->event_model->edit_evenit_byId($evnId);
    // $data['classes'] = $this->event_model->get_classes_event();
    $data['sections'] = $this->event_model->get_all_section_list();
    // echo "<pre>";  print_r($data); die();
    $data['main_content']    = 'event/event_edit';
    $this->load->view('inc/template', $data);
  }

  private function __s3FileUpload($file) {
    if($file['tmp_name'] == '' || $file['name'] == '') {
      return ['status' => 'empty', 'file_name' => ''];
    }        
    return $this->filemanager->uploadFile($file['tmp_name'],$file['name'],'events');
  }

  public function submit_event(){
    
    $file = $this->__s3FileUpload($_FILES['event_pdf_file']);

    $result = $this->event_model->insert_eventDetails($file);
    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Submit Successfully');
      redirect('event');
    }else{
      $this->session->set_flashdata('flashError', 'Something went wrong..');
      redirect('event/add');
   }
  }

  public function publish_events_byId(){
    $stngId = $_POST['stngId'];
    $value = $_POST['value'];
    echo $this->event_model->update_event_status($stngId,$value); 
  }

  public function update_event($id){
    $file = $this->__s3FileUpload($_FILES['event_pdf_file']);
    $result = $this->event_model->update_eventDetails($id, $file);
    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Submit Successfully');
      redirect('event');
    }else{
      $this->session->set_flashdata('flashError', 'Something went wrong..');
      redirect('event/edit');
    }
  }

  public function register_student($event_id){
    $data['event_id'] = $event_id;
    $data['main_content']    = 'event/register_student';
    $this->load->view('inc/template', $data);
  }

  public function get_event_list(){
    $event_id = $_POST['event_id'];
    $result = $this->event_model->get_event_list_for_registration($event_id);
    echo json_encode($result);
  }

  public function add_student_for_event($event_id){
    $data['event_id']  = $event_id;
    $data['event_details'] = $this->event_model->get_event_details_by_id($event_id);
    $data['event_student'] = $this->event_model->get_event_student_list($event_id);
    $data['sections'] = $this->event_model->get_all_section_list();
    $data['main_content'] = 'event/add_student';
    $this->load->view('inc/template', $data);
  }

  public function get_all_class_section_list() {
    $result = $this->event_model->get_all_section_list();
    echo json_encode($result);
  }

  public function remove_event_student(){
    $register_id = $_POST['register_id'];
    echo $this->event_model->remove_student_register_id($register_id);
  }
  public function getSectionwiseStudents(){
    $section_id = $_POST['section_id'];
    $event_id = isset($_POST['event_id']) ? $_POST['event_id'] : null;
    $result = $this->event_model->getSectionwiseStudent_list($section_id, $event_id);
    echo json_encode($result);
  }

  public function insert_student_data(){
      $input = $this->input->post();
      $event_id = $input['event_id'];
      $type = $input['type'];

      $existing_ids = $this->event_model->getExistingevent_register($event_id);

      if ($type == 'class_section') {
        $class_section_id = $input['class_section'];
        $students = $this->event_model->getSectionStudents_event($class_section_id);

        // Get selected sub-events (same as student type)
        $sub_event_data = '';
        if (isset($input['sub_event_check']) && !empty($input['sub_event_check'])) {
          $sub_event_data = $input['sub_event_check']; // Already JSON encoded from frontend
        }

        $event_data = array();
        foreach ($students as $key => $std) {
          if(!in_array($std->id, $existing_ids)) {
            $event_data[] = array(
              'event_id'=>$event_id,
              'student_id'=>$std->id,
              'no_of_person '=> 1,
              'status '=>'active',
              'status '=>'active',
              'registered_by'=> $this->authorization->getAvatarStakeHolderId(),
              'registration_wallet_tx_id'=> 0,
              'safety_depost_amount_wallet_tx_id'=> 0,
              'safety_deposit_amount_return_tx_id'=> 0,
              'safety_deposit_amount_status'=> 'collected',
              'sub_event_id'=>json_encode($sub_event_data) // Add sub-events for class_section type
            );
          }
        }
        $this->event_model->insert_register_student_data($event_data);
      } else if($type == 'student') {
        $stdIds = $input['students'];
        
        $event_data = array();
        foreach ($stdIds as $key => $std) {
          if(!in_array($std, $existing_ids)) {
            $event_data[] = array(
              'event_id'=>$event_id,
              'student_id'=>$std,
              'no_of_person '=> 1,
              'status '=>'active',
              'status '=>'active',
              'registered_by'=> $this->authorization->getAvatarStakeHolderId(),
              'registration_wallet_tx_id'=> 0,
              'safety_depost_amount_wallet_tx_id'=> 0,
              'safety_deposit_amount_return_tx_id'=> 0,
              'safety_deposit_amount_status'=> 'collected',
				      'sub_event_id'=>json_encode($input["sub_event_check"])

            );
          }
        }
        $this->event_model->insert_register_student_data($event_data);
      } 
      redirect('event/add_student_for_event/'.$event_id);
  }

  public function register_student_classwise($evnId){
    $data['event_edit'] = $this->event_model->edit_evenit_byId($evnId); 
    $classess = $this->event_model->get_classSection();
    $regStdSection = $this->event_model->get_regStds_details($evnId);
    $rClass =[];
    foreach ($classess as $key => $val) {
      if(!in_array($val->sectionId, $regStdSection['regSectionIds']))
      array_push($rClass, $val);
    }
    $data['classes'] = $rClass;
    $data['regClasses'] = $regStdSection['regSectionName'];
    // echo "<pre>"; print_r($data['regClasses']); die();
    $data['main_content']    = 'event/register_student';
    $this->load->view('inc/template', $data);
  }

  public function register_students_for_event($eventId){
    $input = $this->input->post();
    $result = $this->event_model->register_stdsbyClassId($eventId, $input);
    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Registered Successfully');
    }else{
      $this->session->set_flashdata('flashError', 'Something went wrong..');
    }
    redirect('event/register_student_classwise/'.$eventId);
  }

  public function delete_classSectionbyEventId($eventId, $classSectionId){
    $result = $this->event_model->delete_classSectionbyId($eventId, $classSectionId);
    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Delete Successfully');
    }else{
      $this->session->set_flashdata('flashError', 'Something went wrong..');
    }
    redirect('event/register_student_classwise/'.$eventId);
  }

  public function sms_notification($eventId){
    $data['event_details'] = $this->event_model->get_event_detailsbyId($eventId);
    // echo "<pre>"; print_r($data['event_details']); die();
    $data['main_content']    = 'event/sms_notification';
    $this->load->view('inc/template', $data);
  }

  public function send_sms_notification($eventId){
    $input = $this->input->post();
    $secArray = explode('_', $input['section_ids']);
    $isUnicode = 0;
    $acadYearId = $this->acad_year->getAcadYearId();
    if(isset($input['unicode'])) {
      $isUnicode = 1;
    }
    if(isset($input['acad_year_id'])) {
      $acadYearId = $input['acad_year_id'];
    }

    $school_name = $this->settings->getSetting('school_name');

    $input_array = array(
      'mode' => $input['communication_type'], 
      'title' => 'Event', 
      'message' => $input['text_content'], 
      'source' => 'Event UI',
      'is_unicode' => $isUnicode,     
      'visible' => 1,
      'send_to' => 'Both',
      'class_send_to' => 'Both',
      'acad_year_id' => $acadYearId
    );
    // $group_data = array();
    // $group_data['class_section'] = array();
    $input_array['class_section_ids'] = array_unique($secArray);
    // $group_data['class_section'] = $input_array['class_section_ids'];
    $response = sendText($input_array);
    if($response['success'] != '') {
      $message = $response['success'];
      $this->session->set_flashdata('flashSuccess', $message);
    }
    if($response['error'] != '') {
      $message = $response['error'];
      $this->session->set_flashdata('flashSuccess', $message);
    }
    redirect('event');
  }

  public function match_qr_code_inDatabase(){
    $content = $_POST['content'];
    $eventId = $_POST['eventId'];
    echo $this->event_model->match_qr_code_content($content,$eventId);
  }
  public function event_qr_code_details($qrCode){
    $data['event'] = $this->event_model->get_student_event_details($qrCode);
    // echo "<pre>"; print_r($data['event']); die();
    $data['main_content']    = 'event/entry_details';
    $this->load->view('inc/template', $data);
  }

  public function submit_event_entry($eventId, $erdId){
    $result = $this->event_model->update_number_of_person_enter($erdId);
    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Successful');
    }else{
      $this->session->set_flashdata('flashError', 'Something went wrong..');
    }
    redirect('event/qr_code_scan/'.$eventId);
  }
  public function event_registration_report($event_id){
    $data['event_id'] = $event_id;
    $data['registered_student'] = $this->event_model->get_registered_student_listby_event_id($event_id);
    // echo "<pre>"; print_r($data['registered_student']); die();
    // $data['event'] = $this->event_model->get_event_list();
    $data['main_content']    = 'event/registration_report';
    $this->load->view('inc/template', $data);
  }

  public function event_registration_student_list(){
    $data['main_content']    = 'event/registration_student_report';
    $this->load->view('inc/template', $data);
  }

  public function event_return_safety_deposit($event_id){
    $data['event_id'] = $event_id;
    $data['classes'] = $this->Student_Model->getClassNames();
    $data['main_content']    = 'event/safety_deposit_student';
    $this->load->view('inc/template', $data);
  }

  public function return_wallet_student_list(){
    $classId = $_POST['classId'];
    $event_id = $_POST['event_id'];
    $studentIds = $this->event_model->return_wallet_student_listby_class($classId, $event_id);
    $studentId = array_chunk($studentIds, 100);
    echo json_encode($studentId);
  }

  public function return_wallet_student_admission_wise(){
    $admin_no = $_POST['admin_no'];
    $event_id = $_POST['event_id'];
    $studentIds = $this->event_model->return_wallet_student_listby_admission($admin_no, $event_id);
    $studentId = array_chunk($studentIds, 100);
    echo json_encode($studentId);
  }

  public function return_wallet_student_stdName_wise(){
    $stdName = $_POST['stdName'];
    $event_id = $_POST['event_id'];
    $studentIds = $this->event_model->return_wallet_student_listby_studentname($stdName, $event_id);
    $studentId = array_chunk($studentIds, 100);
    echo json_encode($studentId);
  }

  public function get_registered_student_event_report(){
    $student_ids = $_POST['student_ids'];
    $event_id = $_POST['event_id'];
    $result = $this->event_model->get_registered_event_student_data($student_ids, $event_id);
    $runningBalanceAmount = $this->event_model->student_wallet_running_balance_amount($student_ids);
    echo json_encode(array('result'=>$result, 'running_balance_amount'=>$runningBalanceAmount));
  }

  public function get_student_event_report(){
    $student_ids = $_POST['student_ids'];
    $result = $this->event_model->get_register_student_data($student_ids);
    echo json_encode($result);
  }

  public function summary_event(){
    $data['event_summary'] = $this->event_model->event_summary_report();
    // echo "<pre>"; print_r($data['event_summary']); die();
    $data['main_content']    = 'event/summary_event';
    $this->load->view('inc/template', $data);
  }

  public function get_student_wallet_summary(){
    $from_date = $_POST['from_date'];
    $to_date = $_POST['to_date'];
    $result = $this->student_wallet_model->get_student_wallet_summary_report($from_date, $to_date);
    echo json_encode($result);
  }

  public function insert_return_student_wise_registration_amount(){

    $return_amount = $this->input->post('student_safety_amount_return');

    $txType = $this->input->post('student_safety_amount_return_status');
    $runningAmount = $this->input->post('studentrunningAmount');

    $event_id = $this->input->post('event_id');
    $remarks = $this->input->post('remarks');
    $this->db->trans_begin();
    foreach ($return_amount as $std_id => $amount) {
      $wallet_tx_data = array(
        'amount' =>  $amount, 
        'student_id' =>  $std_id,
        'transaction_mode' => 'offline', 
        'transaction_type' => $txType[$std_id], 
        'created_by' => $this->authorization->getAvatarId(),
        'status' => 'active',
        'running_balance_amount'=>($txType[$std_id] =='Return') ? $runningAmount[$std_id] +  $amount : $runningAmount[$std_id],
        'remarks'=>$remarks,
        'source_type'=>'Event',
        'source_id'=>$event_id
      );
      $safety_return_tx_Id = $this->event_model->inert_return_wallet_data($wallet_tx_data,'student_wallet_transactions');
      if ($this->db->trans_status() === FALSE) {
        $this->db->trans_rollback();
        echo 0;
        exit();
      }

      $result = $this->event_model->update_return_event_transaction_data($safety_return_tx_Id, $std_id, $event_id, $txType[$std_id]);
      if ($this->db->trans_status() === FALSE) {
        $this->db->trans_rollback();
        echo 0;
        exit();
      }
    }

    $update_walletData = [];
    foreach ($return_amount as $std_id => $amount) {
      $this->db->where('student_id', $std_id);
      $query = $this->db->get('student_wallet');
      $this->db->reset_query();
      if ($query->num_rows() > 0) {
        $update_walletData[] = array(
          'student_id' =>  $std_id, 
          'wallet_amount' =>($txType[$std_id] =='Return') ? $query->row()->wallet_amount + $amount : $query->row()->wallet_amount,
          'hold_amount' => $query->row()->hold_amount -  $amount,
        );
      }
    }

    if (!empty($update_walletData)) {
      $this->student_wallet_model->update_wallet_data($update_walletData,'student_wallet');
    }

    if ($this->db->trans_status() === FALSE) {
      $this->db->trans_rollback();
      echo 0;
      exit();
    }else{
      $this->db->trans_commit();
      echo 1;
    }
    
  }

  public function add_sub_event($event_id){
    $data['event_id'] = $event_id;
    // $data['sub_event'] = $this->event_model->get_all_sub_event_details($event_id);
    $data['event'] = $this->event_model->get_event_details_by_id($event_id);
    $data['main_content']    = 'event/sub_event';
    $this->load->view('inc/template', $data);
  }

  public function submit_sub_event(){
    $event_id = $this->input->post('event_id');
    echo  $this->event_model->insert_sub_eventDetails();
  }

  public function submit_sub_event_csv_array(){
    echo  $this->event_model->insert_sub_event_array_Details();
  }

  public function discard_student_per_event($event_id){
    $data['event_id']  = $event_id;
    $data['event_details'] = $this->event_model->get_event_details_by_id($event_id);
    $data['main_content'] = 'event/discard_student';
    $this->load->view('inc/template', $data);
  }

  public function sub_event_registration_report($event_id){
    $data['event_id']  = $event_id;
    $data['sub_event'] = $this->event_model->get_sub_event_details($event_id);

    $data['event_details'] = $this->event_model->get_event_details_by_id($event_id);
    $data['main_content'] = 'event/sub_event_register';
    $this->load->view('inc/template', $data);
  }

  public function get_sub_event_register_student_list(){
    $subeventId = $_POST['subeventId'];
    $event_id = $_POST['event_id'];
    $result = $this->event_model->get_sub_event_register_student_data($subeventId, $event_id);
    echo json_encode($result);
  }
  
  public function get_all_sub_event_by_id(){
    $event_id = $_POST['event_id'];
    $result = $this->event_model->get_all_sub_event_details($event_id);
    echo json_encode($result);
  }

  public function delete_sub_event(){
    $sub_event_id = $_POST['sub_event_id'];
    echo $this->event_model->delete_sub_event_by_id($sub_event_id);
  }

  public function upload_sub_event_file(){
    $file_path =$_FILES['upload_csv']['tmp_name'];
    $subevent_arr = [];
    $this->load->library('csvimport'); 
    if ($this->csvimport->get_array($file_path)) {
        $subevent_arr = $this->csvimport->get_array($file_path);
    }
    echo json_encode($subevent_arr);
  }

  // Get registration details method (same as parent registration view)
  public function get_registration_details() {
    $event_id = $this->input->post('event_id');
    $student_ids = $this->input->post('student_ids'); // Array of student IDs (optional)

    if (!$event_id) {
      echo json_encode(['success' => false, 'message' => 'Event ID is required']);
      return;
    }

    // Get complete event details (same as parent registration view)
    $event = $this->event_model->get_register_event_details($event_id);
    if (!$event) {
      echo json_encode(['success' => false, 'message' => 'Event not found']);
      return;
    }

    // Get sub events with registration counts (same as parent registration view)
    $sub_event = $this->event_model->get_sub_events_details($event_id);

    // Add registration counts to sub events (same logic as parent view)
    foreach ($sub_event as $val) {
      $val->regCount = 0;
      if (isset($event->subRegister) && array_key_exists($val->id, $event->subRegister)) {
        $val->regCount = $event->subRegister[$val->id];
      }
    }

    // Calculate event amount (same as parent registration view)
    $event_amount = $event->registration_amount + $event->safety_deposit_amount;

    $response = [
      'success' => true,
      'event' => $event,
      'sub_event' => $sub_event,
      'event_amount' => $event_amount,
      'students' => []
    ];

    // If student IDs are provided, get wallet balance for each (optional for admin view)
    if (!empty($student_ids) && is_array($student_ids)) {
      foreach ($student_ids as $student_id) {
        $wallet_balance = $this->event_model->get_wallet_balance_bystudentid($student_id);
        $wallet_amount = !empty($wallet_balance) ? $wallet_balance->wallet_amount : 0;

        // Get student details
        $student = $this->event_model->get_student_details($student_id);

        $response['students'][] = [
          'id' => $student_id,
          'name' => $student ? $student['std_name'] : 'Unknown',
          'wallet_balance' => $wallet_amount,
          'can_register' => ($event_amount <= $wallet_amount)
        ];
      }
    }

    echo json_encode($response);
  }
}