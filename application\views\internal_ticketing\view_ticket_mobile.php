<div class="card panel_new_style">
    <div class="card-header panel_heading_new_style_padding" style="padding-top: 10px;">
        <h3 class="card-title panel_title_new_style">
            <a class="back_anchor" href='<?php echo site_url("internal_ticketing/my_tickets"); ?>'>
                <span class="fa fa-arrow-left"></span>
            </a>
            <strong><?php echo $ticket_data->ticket_item_id; ?></strong>
        </h3>
    </div>
</div>

<!-- Tab Navigation -->
<div class="tab-navigation" style="background: white; border-bottom: 1px solid #e9ecef;">
    <div style="display: flex; justify-content: space-around;">
        <div class="tab-item active" data-tab="details" style="flex: 1; text-align: center; padding: 12px; cursor: pointer; border-bottom: 2px solid #007bff; color: #007bff; font-weight: 500;">
            Details
        </div>
        <div class="tab-item" data-tab="comments" style="flex: 1; text-align: center; padding: 12px; cursor: pointer; border-bottom: 2px solid transparent; color: #6c757d;">
            Comments
        </div>
        <div class="tab-item" data-tab="close-reopen" style="flex: 1; text-align: center; padding: 12px; cursor: pointer; border-bottom: 2px solid transparent; color: #6c757d;">
            Close/reopen
        </div>
    </div>
</div>

<!-- Tab Content -->
<div class="tab-content">
    <!-- Details Tab -->
    <div id="details-tab" class="tab-pane active" style="padding: 16px;">
        <!-- Status and Priority -->
        <div style="display: flex; gap: 8px; margin-bottom: 16px;">
            <?php
            $status_color = '';
            if ($ticket_data->ticket_status == 'Open' || $ticket_data->ticket_status == 'Reopen') {
                $status_color = '#D93E39';
            } else if ($ticket_data->ticket_status == 'In Progress') {
                $status_color = '#FFA500';
            } else if ($ticket_data->ticket_status == 'Resolved') {
                $status_color = '#28a745';
            } else {
                $status_color = '#6c757d';
            }

            $priority_color = '';
            if ($ticket_data->priority_name == 'High') {
                $priority_color = '#D93E39';
            } else if ($ticket_data->priority_name == 'Medium') {
                $priority_color = '#FFA500';
            } else {
                $priority_color = '#28a745';
            }
            ?>
            <span style="background-color: <?php echo $status_color; ?>; color: white; padding: 4px 8px; border-radius: 6px; font-size: 12px; font-weight: 500;">
                <?php echo $ticket_data->ticket_status; ?>
            </span>
            <span style="background-color: <?php echo $priority_color; ?>; color: white; padding: 4px 8px; border-radius: 6px; font-size: 12px; font-weight: 500;">
                <?php echo $ticket_data->priority_name; ?>
            </span>
        </div>

        <!-- Title -->
        <div style="background: #f8f9fa; border-radius: 8px; padding: 12px; margin-bottom: 16px;">
            <div style="display: flex; align-items: center; justify-content: space-between;">
                <span style="font-size: 14px; color: #666;">Title</span>
                <span style="font-size: 14px; color: #333; font-weight: 500;"><?php echo $ticket_data->title; ?></span>
            </div>
        </div>

        <!-- Priority Info -->
        <div style="background: #f8f9fa; border-radius: 8px; padding: 12px; margin-bottom: 16px;">
            <div style="display: flex; align-items: center; justify-content: space-between;">
                <span style="font-size: 14px; color: #666;">Priority</span>
                <span style="font-size: 14px; color: #333; font-weight: 500;"><?php echo $ticket_data->priority_name; ?></span>
            </div>
        </div>

        <!-- Assigned To -->
        <div style="background: #f8f9fa; border-radius: 8px; padding: 12px; margin-bottom: 16px;">
            <div style="display: flex; align-items: center; justify-content: space-between;">
                <span style="font-size: 14px; color: #666;">Assigned To</span>
                <div style="display: flex; align-items: center; gap: 8px;">
                    <span style="font-size: 14px; color: #333; font-weight: 500;"><?php echo $ticket_data->assigned_staff; ?></span>
                    <?php if ($ticket_data->ticket_status == 'Open' || $ticket_data->ticket_status == 'In Progress' || $ticket_data->ticket_status == 'Reopen'): ?>
                        <button onclick="openReassignStaffModal()" style="background: #007bff; color: white; border: none; border-radius: 4px; padding: 4px 8px; font-size: 12px; cursor: pointer;">
                            Reassign
                        </button>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Department -->
        <div style="background: #f8f9fa; border-radius: 8px; padding: 12px; margin-bottom: 16px;">
            <div style="display: flex; align-items: center; justify-content: space-between;">
                <span style="font-size: 14px; color: #666;">Department</span>
                <span style="font-size: 14px; color: #333; font-weight: 500;"><?php echo $ticket_data->department_name; ?></span>
            </div>
        </div>

        <!-- Issue Type -->
        <div style="background: #f8f9fa; border-radius: 8px; padding: 12px; margin-bottom: 16px;">
            <div style="display: flex; align-items: center; justify-content: space-between;">
                <span style="font-size: 14px; color: #666;">Issue Type</span>
                <div style="display: flex; align-items: center; gap: 8px;">
                    <span style="font-size: 14px; color: #333; font-weight: 500;"><?php echo $ticket_data->issue_name; ?></span>
                    <?php if ($ticket_data->ticket_status == 'Open' || $ticket_data->ticket_status == 'In Progress' || $ticket_data->ticket_status == 'Reopen'): ?>
                        <button onclick="openReassignIssueTypeModal()" style="background: #007bff; color: white; border: none; border-radius: 4px; padding: 4px 8px; font-size: 12px; cursor: pointer;">
                            Reassign
                        </button>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Escalation -->
        <div style="background: #f8f9fa; border-radius: 8px; padding: 12px; margin-bottom: 16px;">
            <div style="display: flex; align-items: center; justify-content: space-between;">
                <span style="font-size: 14px; color: #666;">Escalation</span>
                <span style="font-size: 14px; color: #333; font-weight: 500;"><?php echo $ticket_data->escalation_status; ?></span>
            </div>
        </div>

        <!-- Created on -->
        <div style="background: #f8f9fa; border-radius: 8px; padding: 12px; margin-bottom: 16px;">
            <div style="display: flex; align-items: center; justify-content: space-between;">
                <span style="font-size: 14px; color: #666;">Created on</span>
                <span style="font-size: 14px; color: #333; font-weight: 500;"><?php echo $ticket_data->created_on; ?></span>
            </div>
        </div>

        <!-- Deadline -->
        <div style="background: #f8f9fa; border-radius: 8px; padding: 12px; margin-bottom: 16px;">
            <div style="display: flex; align-items: center; justify-content: space-between;">
                <span style="font-size: 14px; color: #666;">Deadline</span>
                <div style="display: flex; align-items: center; gap: 8px;">
                    <span style="font-size: 14px; color: #333; font-weight: 500;">
                        <?php echo !empty($ticket_data->due_date) ? $ticket_data->due_date : 'Not set'; ?>
                    </span>
                    <?php if (!empty($ticket_data->due_date)): ?>
    <?php
    $due_date = DateTime::createFromFormat('d-M-Y', $ticket_data->due_date);
    $today = new DateTime('today'); // Normalizes to 00:00:00 for consistency

    if ($due_date && $due_date >= $today) {
        $days_left = $today->diff($due_date)->days;
        $color = $days_left <= 1 ? '#dc3545' : ($days_left <= 3 ? '#ffc107' : '#28a745');
    ?>
        <span style="background: <?= $color ?>; color: white; padding: 2px 6px; border-radius: 4px; font-size: 10px; font-weight: 500;">
            <?= $days_left ?> day<?= $days_left != 1 ? 's' : '' ?> left
        </span>
    <?php } ?>
<?php endif; ?>

                </div>
            </div>
        </div>

        <!-- Description -->
        <div style="margin-bottom: 16px;">
            <h6 style="margin: 0 0 8px 0; font-size: 16px; font-weight: 600; color: #333;">Description</h6>
            <p style="margin: 0; color: #666; font-size: 14px; line-height: 1.5;">
                <?php echo $ticket_data->description ?: 'No description provided'; ?>
            </p>
        </div>

        <!-- Contributors -->
        <div style="margin-bottom: 16px;">
            <h6 style="margin: 0 0 12px 0; font-size: 16px; font-weight: 600; color: #333;">Contributors</h6>
            <?php if (!empty($ticket_data->secondary_staffs)): ?>
                <div style="display: flex; flex-wrap: wrap; gap: 8px;">
                    <?php
                    $colors = ['#007bff', '#28a745', '#dc3545', '#ffc107', '#6f42c1', '#20c997'];
                    $colorIndex = 0;
                    ?>
                    <?php foreach($ticket_data->secondary_staffs as $contributor): ?>
                        <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                            <div style="width: 32px; height: 32px; border-radius: 50%; background-color: <?php echo $colors[$colorIndex % count($colors)]; ?>; display: flex; align-items: center; justify-content: center;">
                                <span style="color: white; font-size: 14px; font-weight: 500;"><?php echo strtoupper(substr($contributor->staff_name, 0, 1)); ?></span>
                            </div>
                            <span style="font-size: 14px; color: #333;"><?php echo $contributor->staff_name; ?></span>
                        </div>
                        <?php $colorIndex++; ?>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <p style="color: #666; font-size: 14px; margin: 0;">No contributors assigned</p>
            <?php endif; ?>
        </div>

        <!-- Attachments -->
        <div style="margin-bottom: 16px;">
            <h6 style="margin: 0 0 12px 0; font-size: 16px; font-weight: 600; color: #333;">Attachments</h6>
            <?php if (!empty($ticket_data->doc) && count($ticket_data->doc) > 0): ?>
                <?php foreach($ticket_data->doc as $index => $file_url): ?>
                    <div style="display: flex; align-items: center; gap: 12px; background: #f8f9fa; border-radius: 8px; padding: 12px; margin-bottom: 8px;">
                        <div style="width: 40px; height: 40px; background: #dc3545; border-radius: 8px; display: flex; align-items: center; justify-content: center;">
                            <span style="color: white; font-size: 12px; font-weight: 600;">📎</span>
                        </div>
                        <div style="flex: 1;">
                            <div style="font-size: 14px; font-weight: 500; color: #333;">
                                <a href="<?php echo $file_url; ?>" target="_blank" style="color: #007bff; text-decoration: none;">
                                    Attachment <?php echo $index + 1; ?>
                                </a>
                            </div>
                            <div style="font-size: 12px; color: #666;">Click to view</div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <p style="color: #666; font-size: 14px; margin: 0;">No attachments</p>
            <?php endif; ?>
        </div>
    </div>

    <!-- Comments Tab -->
    <div id="comments-tab" class="tab-pane" style="padding: 16px; display: none;">
        <!-- Add Comment Button -->
        <div style="margin-bottom: 20px; text-align: right;">
            <button onclick="openCommentModal()" style="background: #007bff; color: white; border: none; border-radius: 50%; width: 40px; height: 40px; font-size: 16px; cursor: pointer; box-shadow: 0 2px 8px rgba(0,123,255,0.3);">
                <i class="fa fa-plus"></i>
            </button>
        </div>

        <div id="comments-list">
            <?php if (!empty($ticket_comments)): ?>
                <?php foreach($ticket_comments as $comment): ?>
                    <div style="background: #f8f9fa; border-radius: 12px; padding: 16px; margin-bottom: 16px;">
                        <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 12px;">
                            <div style="width: 32px; height: 32px; border-radius: 50%; background-color: #6c757d; display: flex; align-items: center; justify-content: center;">
                                <span style="color: white; font-size: 14px; font-weight: 500;"><?php echo substr($comment->commented_by, 0, 1); ?></span>
                            </div>
                            <span style="font-size: 14px; font-weight: 500; color: #333;"><?php echo $comment->commented_by; ?></span>
                        </div>
                        <p style="margin: 0 0 12px 0; color: #666; font-size: 14px; line-height: 1.4;">
                            <?php echo $comment->ticket_comment; ?>
                        </p>
                        <?php if (!empty($comment->file_paths)): ?>
                            <?php foreach($comment->file_paths as $file): ?>
                                <?php if (!empty($file->file_path)): ?>
                                    <div style="display: flex; align-items: center; gap: 12px; background: white; border-radius: 8px; padding: 12px; margin-bottom: 8px;">
                                        <div style="width: 40px; height: 40px; background: #dc3545; border-radius: 8px; display: flex; align-items: center; justify-content: center;">
                                            <span style="color: white; font-size: 12px; font-weight: 600;">PDF</span>
                                        </div>
                                        <div style="flex: 1;">
                                            <div style="font-size: 14px; font-weight: 500; color: #333;">Attachment</div>
                                            <div style="font-size: 12px; color: #666;">
                                                <a href="<?php echo $file->file_path; ?>" target="_blank" style="color: #007bff;">View File</a>
                                            </div>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        <?php endif; ?>
                        <span style="font-size: 12px; color: #999;"><?php echo $comment->commented_on; ?></span>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div style="text-align: center; padding: 40px;">
                    <p style="color: #666; margin: 0;">No comments yet</p>
                </div>
            <?php endif; ?>
        </div>


    </div>

    <!-- Close/Reopen Tab -->
    <div id="close-reopen-tab" class="tab-pane" style="padding: 16px; display: none;">
        <div style="margin-bottom: 20px;">
            <h6 style="margin: 0 0 12px 0; font-size: 16px; font-weight: 600; color: #333;">Update Status</h6>
            <select id="status-dropdown" style="width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 8px; font-size: 14px; background-color: white; -webkit-appearance: none; -moz-appearance: none; appearance: none; background-image: url('data:image/svg+xml;charset=US-ASCII,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 4 5\"><path fill=\"%23666\" d=\"M2 0L0 2h4zm0 5L0 3h4z\"/></svg>'); background-repeat: no-repeat; background-position: right 12px center; background-size: 12px;">
                <?php if($ticket_data->ticket_status == 'Resolved'): ?>
                    <option value="Reopen">Reopen</option>
                    <?php if($this->settings->getSetting('enable_close_option_in_internal_ticketing')): ?>
                        <option value="Closed">Close</option>
                    <?php endif; ?>
                <?php elseif($ticket_data->ticket_status == 'Closed'): ?>
                    <option value="Reopen">Reopen</option>
                <?php else: ?>
                    <option value="Resolved">Resolve</option>
                <?php endif; ?>
            </select>
        </div>

        <div style="margin-bottom: 20px;">
            <h6 style="margin: 0 0 12px 0; font-size: 16px; font-weight: 600; color: #333;">Give Rating</h6>
            <div class="star-rating" style="display: flex; gap: 8px; margin-bottom: 12px;">
                <span class="star" data-rating="1" style="font-size: 24px; color: #ddd; cursor: pointer;">★</span>
                <span class="star" data-rating="2" style="font-size: 24px; color: #ddd; cursor: pointer;">★</span>
                <span class="star" data-rating="3" style="font-size: 24px; color: #ddd; cursor: pointer;">★</span>
                <span class="star" data-rating="4" style="font-size: 24px; color: #ddd; cursor: pointer;">★</span>
                <span class="star" data-rating="5" style="font-size: 24px; color: #ddd; cursor: pointer;">★</span>
            </div>
            <input type="hidden" id="selected-rating" value="0">
        </div>

        <div style="margin-bottom: 20px;">
            <h6 style="margin: 0 0 12px 0; font-size: 16px; font-weight: 600; color: #333;">Remarks <span style="color: red;">*</span></h6>
            <textarea id="status-remarks" placeholder="Write Remarks" maxlength="500" style="width: 100%; min-height: 120px; border: 1px solid #ddd; border-radius: 8px; padding: 12px; font-size: 14px; resize: vertical;" required oninput="updateCharCount(this, 'remarks-char-count')"></textarea>
            <div style="text-align: right; margin-top: 4px;">
                <small id="remarks-char-count" style="color: #666;">0/500 characters</small>
            </div>
        </div>

        <button onclick="updateTicketStatus()" style="background: #007bff; color: white; border: none; border-radius: 8px; padding: 12px 24px; font-size: 14px; font-weight: 500; cursor: pointer; width: 100%;">
            Submit
        </button>
    </div>
</div>

<style>
.tab-item.active {
    border-bottom-color: #007bff !important;
    color: #007bff !important;
    font-weight: 500;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}
</style>

<script>
$(document).ready(function() {
    // Tab switching
    $('.tab-item').click(function() {
        var tabId = $(this).data('tab');
        showTab(tabId);
    });



    // Star rating functionality
    $('.star').click(function() {
        var rating = $(this).data('rating');
        $('#selected-rating').val(rating);

        // Update star colors
        $('.star').each(function(index) {
            if (index < rating) {
                $(this).css('color', '#ffc107');
            } else {
                $(this).css('color', '#ddd');
            }
        });
    });

    // Star hover effect
    $('.star').hover(
        function() {
            var rating = $(this).data('rating');
            $('.star').each(function(index) {
                if (index < rating) {
                    $(this).css('color', '#ffc107');
                } else {
                    $(this).css('color', '#ddd');
                }
            });
        },
        function() {
            var selectedRating = $('#selected-rating').val();
            $('.star').each(function(index) {
                if (index < selectedRating) {
                    $(this).css('color', '#ffc107');
                } else {
                    $(this).css('color', '#ddd');
                }
            });
        }
    );
});



function updateCharCount(textarea, counterId) {
    var maxLength = textarea.getAttribute('maxlength');
    var currentLength = textarea.value.length;
    document.getElementById(counterId).textContent = currentLength + '/' + maxLength + ' characters';

    // Change color when approaching limit
    var counter = document.getElementById(counterId);
    if (currentLength > maxLength * 0.9) {
        counter.style.color = '#dc3545';
    } else if (currentLength > maxLength * 0.7) {
        counter.style.color = '#ffc107';
    } else {
        counter.style.color = '#666';
    }
}

function updateTicketStatus() {
    var status = $('#status-dropdown').val();
    var remarks = $('#status-remarks').val();
    var rating = $('#selected-rating').val();

    if (!remarks.trim()) {
        Swal.fire({
            title: 'Validation Error',
            text: 'Please enter remarks',
            icon: 'warning',
            confirmButtonText: 'OK',
            confirmButtonColor: '#007bff'
        });
        return;
    }

    if (status === 'Closed' && rating == 0) {
        Swal.fire({
            title: 'Validation Error',
            text: 'Please provide a rating before closing the ticket',
            icon: 'warning',
            confirmButtonText: 'OK',
            confirmButtonColor: '#007bff'
        });
        return;
    }

    $.ajax({
        url: '<?php echo site_url('internal_ticketing/update_ticket_status'); ?>',
        type: 'POST',
        data: {
            ticket_id: '<?php echo $ticket_data->ticket_id; ?>',
            status: status,
            remarks: remarks,
            rating: rating
        },
        success: function(response) {
            try {
                var result = JSON.parse(response);
                if (result.status === 'success') {
                    Swal.fire({
                        title: 'Success!',
                        text: 'Status updated successfully',
                        icon: 'success',
                        confirmButtonText: 'OK',
                        confirmButtonColor: '#007bff'
                    }).then((result) => {
                        location.reload();
                    });
                } else {
                    Swal.fire({
                        title: 'Error!',
                        text: result.message || 'Error updating status',
                        icon: 'error',
                        confirmButtonText: 'OK',
                        confirmButtonColor: '#007bff'
                    });
                }
            } catch(e) {
                // If response is not JSON, check if it's a success (1) or failure (0)
                if (response == '1' || response == 1) {
                    Swal.fire({
                        title: 'Success!',
                        text: 'Status updated successfully',
                        icon: 'success',
                        confirmButtonText: 'OK',
                        confirmButtonColor: '#007bff'
                    }).then((result) => {
                        location.reload();
                    });
                } else {
                    Swal.fire({
                        title: 'Error!',
                        text: 'Error updating status',
                        icon: 'error',
                        confirmButtonText: 'OK',
                        confirmButtonColor: '#007bff'
                    });
                }
            }
        },
        error: function(xhr, status, error) {
            console.log('AJAX Error:', error);
            console.log('Response:', xhr.responseText);
            Swal.fire({
                title: 'Error!',
                text: 'Network error updating status',
                icon: 'error',
                confirmButtonText: 'OK',
                confirmButtonColor: '#007bff'
            });
        }
    });
}

// Comment Modal Functions
function openCommentModal() {
    var modal = document.getElementById('commentModal');
    if (modal) {
        modal.style.display = 'block';
        document.body.style.overflow = 'hidden'; // Prevent background scrolling
    }
}

function closeCommentModal() {
    var modal = document.getElementById('commentModal');
    var form = document.getElementById('comment-modal-form');
    var fileList = document.getElementById('comment-file-list');

    if (modal) {
        modal.style.display = 'none';
        document.body.style.overflow = 'auto'; // Restore scrolling
    }

    if (form) {
        form.reset();
    }

    if (fileList) {
        fileList.innerHTML = '';
    }

    uploadedCommentFiles = [];
}

// File upload variables
var uploadedCommentFiles = [];
var fileCounter = 0;

// Initialize file upload functionality when document is ready
$(document).ready(function() {
    // Handle file selection
    $(document).on('change', '#comment-files', function(e) {
        var files = e.target.files;
        for (var i = 0; i < files.length; i++) {
            var file = files[i];
            fileCounter++;

            // Validate file size (10MB limit)
            if (file.size > 10 * 1024 * 1024) {
                alert('File "' + file.name + '" is too large. Maximum size is 10MB.');
                continue;
            }

            // Add file to display list
            addFileToCommentList(file, fileCounter);

            // Upload file immediately
            uploadCommentFile(file, fileCounter);
        }

        // Clear the input
        e.target.value = '';
    });
});

function addFileToCommentList(file, fileId) {
    var fileList = document.getElementById('comment-file-list');
    if (!fileList) {
        console.error('comment-file-list element not found');
        return;
    }

    var fileDiv = document.createElement('div');
    fileDiv.id = 'comment-file-' + fileId;
    fileDiv.style.cssText = 'display: flex; align-items: center; justify-content: space-between; padding: 8px; background: #f8f9fa; border-radius: 6px; margin-bottom: 8px;';

    fileDiv.innerHTML =
        '<div style="display: flex; align-items: center; gap: 8px;">' +
            '<i class="fa fa-file" style="color: #6c757d;"></i>' +
            '<span style="font-size: 14px; color: #333;">' + file.name + '</span>' +
            '<span id="comment-file-status-' + fileId + '" style="font-size: 12px; color: #ffc107;">Uploading...</span>' +
        '</div>' +
        '<button type="button" onclick="removeCommentFile(' + fileId + ')" style="background: none; border: none; color: #dc3545; cursor: pointer;">' +
            '<i class="fa fa-times"></i>' +
        '</button>';

    fileList.appendChild(fileDiv);
}

function uploadCommentFile(file, fileId) {
    // Get signed URL first
    $.ajax({
        url: '<?php echo site_url("S3_controller/getSignedUrl"); ?>',
        type: 'POST',
        data: {
            filename: file.name,
            file_type: file.type,
            folder: 'internal_ticketing_comments'
        },
        success: function(response) {
            try {
                var data = JSON.parse(response);

                if (!data.signedUrl || !data.path) {
                    document.getElementById('comment-file-status-' + fileId).innerHTML = '<span style="color: #dc3545;">Invalid Response</span>';
                    return;
                }

                // Upload to S3 using the same method as other parts of the system
                $.ajax({
                    url: data.signedUrl,
                    type: 'PUT',
                    data: file,
                    processData: false,
                    contentType: false,
                    headers: {
                        "Content-Type": file.type,
                        "x-amz-acl": "public-read"
                    },
                    xhr: function() {
                        var xhr = new window.XMLHttpRequest();
                        // Upload progress tracking
                        xhr.upload.addEventListener("progress", function(evt) {
                            if (evt.lengthComputable) {
                                var percentComplete = Math.round((evt.loaded / evt.total) * 100);
                                document.getElementById('comment-file-status-' + fileId).innerHTML = '<span style="color: #ffc107;">' + percentComplete + '%</span>';
                            }
                        }, false);
                        return xhr;
                    },
                    success: function() {
                        // File uploaded successfully
                        document.getElementById('comment-file-status-' + fileId).innerHTML = '<span style="color: #28a745;">Uploaded</span>';
                        uploadedCommentFiles.push({
                            path: data.path,
                            name: file.name
                        });
                    },
                    error: function(xhr, status, error) {
                        console.log('S3 Upload Error:', error);
                        console.log('XHR:', xhr);
                        document.getElementById('comment-file-status-' + fileId).innerHTML = '<span style="color: #dc3545;">Failed</span>';
                    }
                });
            } catch(e) {
                console.log('JSON Parse Error:', e);
                document.getElementById('comment-file-status-' + fileId).innerHTML = '<span style="color: #dc3545;">Parse Error</span>';
            }
        },
        error: function(xhr, status, error) {
            console.log('Signed URL Error:', error);
            document.getElementById('comment-file-status-' + fileId).innerHTML = '<span style="color: #dc3545;">URL Failed</span>';
        }
    });
}

function removeCommentFile(fileId) {
    document.getElementById('comment-file-' + fileId).remove();
    // Remove from uploaded files array if needed
    // This is a simplified version - you might want to track which files to remove
}

// Handle comment form submission
$(document).ready(function() {
    $(document).on('submit', '#comment-modal-form', function(e) {
        e.preventDefault();

        var comment = $(this).find('textarea[name="comment"]').val();
        if (!comment.trim()) {
            alert('Please enter a comment');
            return;
        }

        // Show loading state
        $('#modal-submit-text').hide();
        $('#modal-submit-loading').show();
        $('#modal-submit-btn').prop('disabled', true);

        // Debug logging
        console.log('Submitting comment:', {
            ticket_id: '<?php echo $ticket_data->ticket_id; ?>',
            comment: comment,
            file_paths: uploadedCommentFiles
        });

        // Submit comment with attachments
        $.ajax({
            url: '<?php echo site_url('internal_ticketing/add_comment'); ?>',
            type: 'POST',
            data: {
                ticket_id: '<?php echo $ticket_data->ticket_id; ?>',
                comment: comment,
                file_paths: uploadedCommentFiles.length > 0 ? uploadedCommentFiles : null
            },
        success: function(response) {
            console.log('Server response:', response);
            try {
                var result = JSON.parse(response);
                console.log('Parsed result:', result);

                if (result.status === 'success') {
                    closeCommentModal();
                    showTab('comments');
                    setTimeout(function() {
                        location.reload();
                    }, 500);
                } else {
                    console.log('Error result:', result);
                    alert(result.message || 'Error adding comment');
                    if (result.debug) {
                        console.log('Debug info:', result.debug);
                    }
                    resetModalButton();
                }
            } catch(e) {
                console.log('JSON parse error:', e);
                console.log('Raw response:', response);
                if (response == '1' || response == 1) {
                    closeCommentModal();
                    showTab('comments');
                    setTimeout(function() {
                        location.reload();
                    }, 500);
                } else {
                    alert('Error adding comment. Check console for details.');
                    resetModalButton();
                }
            }
        },
        error: function(xhr, status, error) {
            console.log('AJAX Error:', error);
            console.log('XHR:', xhr);
            console.log('Status:', status);
            console.log('Response Text:', xhr.responseText);
            alert('Network error adding comment. Check console for details.');
            resetModalButton();
        }
    });
});

function resetModalButton() {
    $('#modal-submit-text').show();
    $('#modal-submit-loading').hide();
    $('#modal-submit-btn').prop('disabled', false);
}
});

// Global function to show specific tab
function showTab(tabId) {
    // Remove active class from all tabs and content
    $('.tab-item').removeClass('active').css({
        'border-bottom-color': 'transparent',
        'color': '#6c757d'
    });
    $('.tab-pane').removeClass('active').hide();

    // Add active class to target tab
    $('.tab-item[data-tab="' + tabId + '"]').addClass('active').css({
        'border-bottom-color': '#007bff',
        'color': '#007bff'
    });

    // Show corresponding content
    $('#' + tabId + '-tab').addClass('active').show();
}

// Reassignment Modal Functions
function openReassignStaffModal() {
    document.getElementById('reassignStaffModal').style.display = 'block';
    document.body.style.overflow = 'hidden';
}

function closeReassignStaffModal() {
    document.getElementById('reassignStaffModal').style.display = 'none';
    document.body.style.overflow = 'auto';
    document.getElementById('reassign-staff-form').reset();
}

function openReassignIssueTypeModal() {
    document.getElementById('reassignIssueTypeModal').style.display = 'block';
    document.body.style.overflow = 'hidden';
}

function closeReassignIssueTypeModal() {
    document.getElementById('reassignIssueTypeModal').style.display = 'none';
    document.body.style.overflow = 'auto';
    document.getElementById('reassign-issue-type-form').reset();
}

// Reassign Staff Function
function reassignStaff() {
    var selectedStaff = $('#selected_staff').val();
    var ticketId = '<?php echo $ticket_data->ticket_id; ?>';

    if (!selectedStaff) {
        alert('Please select a staff member');
        return;
    }

    // Show loading state
    $('#reassign-staff-submit-text').hide();
    $('#reassign-staff-submit-loading').show();
    $('#reassign-staff-submit-btn').prop('disabled', true);

    $.ajax({
        url: '<?php echo site_url('internal_ticketing/update_ticket_assignee'); ?>',
        type: 'POST',
        data: {
            ticket_id: ticketId,
            selected_staff: selectedStaff,
            reassigned_by: '<?php echo $this->authorization->getAvatarStakeHolderId(); ?>'
        },
        success: function(response) {
            closeReassignStaffModal();
            alert('Staff reassigned successfully');
            location.reload();
        },
        error: function(xhr, status, error) {
            console.log('Error:', error);
            alert('Error reassigning staff');
            resetReassignStaffButton();
        }
    });
}

// Reassign Issue Type Function
function reassignIssueType() {
    var selectedIssueType = $('#selected_issue_type').val();
    var ticketId = '<?php echo $ticket_data->ticket_id; ?>';

    if (!selectedIssueType) {
        alert('Please select an issue type');
        return;
    }

    // Show loading state
    $('#reassign-issue-type-submit-text').hide();
    $('#reassign-issue-type-submit-loading').show();
    $('#reassign-issue-type-submit-btn').prop('disabled', true);

    $.ajax({
        url: '<?php echo site_url('internal_ticketing/update_ticket_issue_type'); ?>',
        type: 'POST',
        data: {
            ticket_id: ticketId,
            selected_issue_type: selectedIssueType,
            reassigned_issuetype_by: '<?php echo $this->authorization->getAvatarStakeHolderId(); ?>'
        },
        success: function(response) {
            closeReassignIssueTypeModal();
            alert('Issue type reassigned successfully');
            location.reload();
        },
        error: function(xhr, status, error) {
            console.log('Error:', error);
            alert('Error reassigning issue type');
            resetReassignIssueTypeButton();
        }
    });
}

function resetReassignStaffButton() {
    $('#reassign-staff-submit-text').show();
    $('#reassign-staff-submit-loading').hide();
    $('#reassign-staff-submit-btn').prop('disabled', false);
}

function resetReassignIssueTypeButton() {
    $('#reassign-issue-type-submit-text').show();
    $('#reassign-issue-type-submit-loading').hide();
    $('#reassign-issue-type-submit-btn').prop('disabled', false);
}
</script>

<!-- Comment Modal -->
<div id="commentModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; border-radius: 12px; padding: 24px; width: 90%; max-width: 500px; max-height: 80vh; overflow-y: auto;">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
            <h5 style="margin: 0; font-size: 18px; font-weight: 600; color: #333;">Add Comment</h5>
            <button onclick="closeCommentModal()" style="background: none; border: none; font-size: 24px; color: #666; cursor: pointer;">&times;</button>
        </div>

        <form id="comment-modal-form">
            <input type="hidden" name="ticket_id" value="<?php echo $ticket_data->ticket_id; ?>">

            <!-- Comment Text -->
            <div style="margin-bottom: 16px;">
                <label style="display: block; margin-bottom: 8px; font-size: 14px; font-weight: 500; color: #333;">Comment <span style="color: red;">*</span></label>
                <textarea name="comment" placeholder="Write your comment..." maxlength="1000" style="width: 100%; min-height: 100px; border: 1px solid #ddd; border-radius: 8px; padding: 12px; font-size: 14px; resize: vertical;" required oninput="updateCharCount(this, 'modal-comment-char-count')"></textarea>
                <div style="text-align: right; margin-top: 4px;">
                    <small id="modal-comment-char-count" style="color: #666;">0/1000 characters</small>
                </div>
            </div>

            <!-- File Attachments -->
            <div style="margin-bottom: 16px;">
                <label style="display: block; margin-bottom: 8px; font-size: 14px; font-weight: 500; color: #333;">Attachments (Optional)</label>
                <input type="file" id="comment-files" multiple accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.gif" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 8px;">
                <small style="color: #666; font-size: 12px;">Supported formats: PDF, DOC, DOCX, JPG, PNG, GIF (Max 10MB each)</small>

                <!-- File List -->
                <div id="comment-file-list" style="margin-top: 12px;"></div>
            </div>

            <!-- Submit Button -->
            <div style="display: flex; gap: 12px; justify-content: flex-end;">
                <button type="button" onclick="closeCommentModal()" style="background: #6c757d; color: white; border: none; border-radius: 8px; padding: 12px 24px; font-size: 14px; cursor: pointer;">
                    Cancel
                </button>
                <button type="submit" id="modal-submit-btn" style="background: #007bff; color: white; border: none; border-radius: 8px; padding: 12px 24px; font-size: 14px; font-weight: 500; cursor: pointer;">
                    <span id="modal-submit-text">Add Comment</span>
                    <span id="modal-submit-loading" style="display: none;">
                        <i class="fa fa-spinner fa-spin"></i> Adding...
                    </span>
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Reassign Staff Modal -->
<div id="reassignStaffModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; border-radius: 12px; padding: 24px; width: 90%; max-width: 500px; max-height: 80vh; overflow-y: auto;">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
            <h5 style="margin: 0; font-size: 18px; font-weight: 600; color: #333;">Reassign Staff</h5>
            <button onclick="closeReassignStaffModal()" style="background: none; border: none; font-size: 24px; color: #666; cursor: pointer;">&times;</button>
        </div>

        <form id="reassign-staff-form">
            <input type="hidden" name="ticket_id" value="<?php echo $ticket_data->ticket_id; ?>">
            <input type="hidden" name="reassigned_by" value="<?php echo $this->authorization->getAvatarStakeHolderId(); ?>">

            <!-- Staff Selection -->
            <div style="margin-bottom: 16px;">
                <label style="display: block; margin-bottom: 8px; font-size: 14px; font-weight: 500; color: #333;">Select Staff <span style="color: red;">*</span></label>
                <select id="selected_staff" name="selected_staff" style="width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 8px; font-size: 14px;" required>
                    <option value="">Select Staff</option>
                    <?php if (!empty($staff_data)): ?>
                        <?php foreach($staff_data as $staff): ?>
                            <?php if ($staff->staff_id == $ticket_data->assigned_to): ?>
                                <option disabled value="<?php echo $staff->staff_id; ?>"><?php echo $staff->staff_name; ?> (Current)</option>
                            <?php else: ?>
                                <option value="<?php echo $staff->staff_id; ?>"><?php echo $staff->staff_name; ?></option>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </select>
            </div>

            <!-- Submit Button -->
            <div style="display: flex; gap: 12px; justify-content: flex-end;">
                <button type="button" onclick="closeReassignStaffModal()" style="background: #6c757d; color: white; border: none; border-radius: 8px; padding: 12px 24px; font-size: 14px; cursor: pointer;">
                    Cancel
                </button>
                <button type="button" onclick="reassignStaff()" id="reassign-staff-submit-btn" style="background: #007bff; color: white; border: none; border-radius: 8px; padding: 12px 24px; font-size: 14px; font-weight: 500; cursor: pointer;">
                    <span id="reassign-staff-submit-text">Reassign</span>
                    <span id="reassign-staff-submit-loading" style="display: none;">
                        <i class="fa fa-spinner fa-spin"></i> Reassigning...
                    </span>
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Reassign Issue Type Modal -->
<div id="reassignIssueTypeModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; border-radius: 12px; padding: 24px; width: 90%; max-width: 500px; max-height: 80vh; overflow-y: auto;">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
            <h5 style="margin: 0; font-size: 18px; font-weight: 600; color: #333;">Reassign Issue Type</h5>
            <button onclick="closeReassignIssueTypeModal()" style="background: none; border: none; font-size: 24px; color: #666; cursor: pointer;">&times;</button>
        </div>

        <form id="reassign-issue-type-form">
            <input type="hidden" name="ticket_id" value="<?php echo $ticket_data->ticket_id; ?>">
            <input type="hidden" name="reassigned_issuetype_by" value="<?php echo $this->authorization->getAvatarStakeHolderId(); ?>">

            <!-- Issue Type Selection -->
            <div style="margin-bottom: 16px;">
                <label style="display: block; margin-bottom: 8px; font-size: 14px; font-weight: 500; color: #333;">Select Issue Type <span style="color: red;">*</span></label>
                <select id="selected_issue_type" name="selected_issue_type" style="width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 8px; font-size: 14px;" required>
                    <option value="">Select Issue Type</option>
                    <?php if (!empty($ticket_data->issue_type_data)): ?>
                        <?php foreach($ticket_data->issue_type_data as $issue_type): ?>
                            <?php if ($issue_type->issue_type_id == $ticket_data->issue_type): ?>
                                <option disabled value="<?php echo $issue_type->issue_type_id; ?>"><?php echo $issue_type->issue_type_name; ?> (Current)</option>
                            <?php else: ?>
                                <option value="<?php echo $issue_type->issue_type_id; ?>"><?php echo $issue_type->issue_type_name; ?></option>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </select>
            </div>

            <!-- Submit Button -->
            <div style="display: flex; gap: 12px; justify-content: flex-end;">
                <button type="button" onclick="closeReassignIssueTypeModal()" style="background: #6c757d; color: white; border: none; border-radius: 8px; padding: 12px 24px; font-size: 14px; cursor: pointer;">
                    Cancel
                </button>
                <button type="button" onclick="reassignIssueType()" id="reassign-issue-type-submit-btn" style="background: #007bff; color: white; border: none; border-radius: 8px; padding: 12px 24px; font-size: 14px; font-weight: 500; cursor: pointer;">
                    <span id="reassign-issue-type-submit-text">Reassign</span>
                    <span id="reassign-issue-type-submit-loading" style="display: none;">
                        <i class="fa fa-spinner fa-spin"></i> Reassigning...
                    </span>
                </button>
            </div>
        </form>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
