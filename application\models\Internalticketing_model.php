<?php
  defined('BASEPATH') OR exit('No direct script access allowed');

class Internalticketing_model extends CI_Model {
    public function __construct() {
        parent::__construct();
        $this->load->library('filemanager');
    }

    public function Kolkata_datetime(){
        $timezone = new DateTimeZone("Asia/Kolkata" );
        $date = new DateTime();
        $date->setTimezone($timezone );
        $dtobj = $date->format('Y-m-d H:i:s');
        return $dtobj;
    }

    public function get_open_ticket_count_details_created_by_me(){
        $stakeholder_id = $this->authorization->getAvatarStakeHolderId();

        $result = $this->db_readonly->select('count(it.id) as open_ticket_count')
        ->from("internal_ticketing_items it")
        ->where_in('it.ticket_status', ['Open','In Progress','Reopen'])
        ->where('it.reported_by', $stakeholder_id)
        ->get()->row();
        return $result;
    }

    public function get_resolved_ticket_count_details_created_by_me(){
        $stakeholder_id = $this->authorization->getAvatarStakeHolderId();

        $result = $this->db_readonly->select('count(it.id) as resolved_ticket_count')
        ->from("internal_ticketing_items it")
        ->where('it.ticket_status', 'Resolved')
        ->where('it.reported_by', $stakeholder_id)
        ->get()->row();
        return $result;
    }


    public function tickets_assigned_to_me(){
        $stakeholder_id = $this->authorization->getAvatarStakeHolderId();

        $result = $this->db_readonly->select('count(it.id) as assigned_to_me')
        ->from("internal_ticketing_items it")
        ->where('it.assigned_to', $stakeholder_id)
        ->get()->row();
        return $result;
    }


    public function get_assigned_to_my_dept_ticket_count_details(){
        $stakeholder_id = $this->authorization->getAvatarStakeHolderId();

        $logged_in_staff_department = $this->db_readonly->select('sm.department')
        ->from('staff_master sm')
        ->where('sm.id', $stakeholder_id)
        ->get()->row();

        if (empty($logged_in_staff_department)) {
            $result = new stdClass();
            $result->assigned_to_my_dept = 0;
            return $result;
        }
        $result = $this->db_readonly->select('count(it.id) as assigned_to_my_dept')
        ->from("internal_ticketing_items it")
        ->where('it.assigned_to_department_id', $logged_in_staff_department->department)
        ->get()->row();
        return $result;
    }


    public function get_all_tickets_data(){
        $stakeholder_id = $this->authorization->getAvatarStakeHolderId();
        // print_r($stakeholder_id);die();

        $logged_in_staff_department = $this->db_readonly->select('sm.department')
        ->from('staff_master sm')
        ->where('sm.id', $stakeholder_id)
        ->get()->row();

        $data = $this->db_readonly->select('count(it.id) as open_ticket_count')
        ->from("internal_ticketing_items it")
        ->where_in('it.ticket_status', ['Open','In Progress','Reopen'])
        // ->where('it.assigned_to', $stakeholder_id)
        ->where('it.assigned_to_department_id', $logged_in_staff_department->department)
        // ->where('it.assigned_to_department_id', $logged_in_staff_department->department)
        ->get()->row();

        $closed_tickets = $this->db_readonly->select('count(it.id) as closed_ticket_count')
        ->from("internal_ticketing_items it")
        ->where('it.ticket_status', 'Closed')
        // ->where('it.assigned_to', $stakeholder_id)
        ->where('it.assigned_to_department_id', $logged_in_staff_department->department)
        // ->where('it.assigned_to_department_id', $logged_in_staff_department->department)
        ->get()->row();
        $data->closed_ticket_count = $closed_tickets->closed_ticket_count;

        $reopen_tickets = $this->db_readonly->select('count(it.id) as reopened_ticket_count')
        ->from("internal_ticketing_items it")
        ->where('it.ticket_status', 'Reopen')
        // ->where('it.assigned_to', $stakeholder_id)
        ->where('it.assigned_to_department_id', $logged_in_staff_department->department)
        // ->where('it.assigned_to_department_id', $logged_in_staff_department->department)
        ->get()->row();
        $data->reopened_ticket_count = $reopen_tickets->reopened_ticket_count;

        $resolved_tickets = $this->db_readonly->select('count(it.id) as resolved_ticket_count')
        ->from("internal_ticketing_items it")
        ->where('it.ticket_status', 'Resolved')
        // ->where('it.assigned_to', $stakeholder_id)
        ->where('it.assigned_to_department_id', $logged_in_staff_department->department)
        // ->where('it.assigned_to_department_id', $logged_in_staff_department->department)
        ->get()->row();
        $data->resolved_ticket_count = $resolved_tickets->resolved_ticket_count;

        $in_progress_tickets = $this->db_readonly->select('count(it.id) as in_progress_ticket_count')
        ->from("internal_ticketing_items it")
        ->where('it.ticket_status', 'In Progress')
        // ->where('it.assigned_to', $stakeholder_id)
        ->where('it.assigned_to_department_id', $logged_in_staff_department->department)
        ->get()->row();
        $data->in_progress_ticket_count = $in_progress_tickets->in_progress_ticket_count;

        return $data;
    }

    public function get_department_wise_tickets_priority_data(){
        $priority_array = [];
        $logged_in_user = $this->authorization->getAvatarStakeHolderId();
        $logged_in_staff_department = $this->db_readonly->select('sm.department')
        ->from('staff_master sm')
        ->where('sm.id', $logged_in_user)
        ->get()->row();

        $priorities = $this->db_readonly->select("ip.priority_name, ip.id as priority_id")
        ->from('internal_ticketing_priority ip')
        ->where('ip.status', 1)
        ->get()->result();

        foreach ($priorities as $key) {
            $result = $this->db_readonly->select("count(it.id) as count")
            ->from('internal_ticketing_items it')
            ->where('it.priority', $key->priority_id)
            ->where('it.assigned_to_department_id', $logged_in_staff_department->department)
            ->get()->row();
            $priority_array["$key->priority_name"] = $result->count;
        }
        return $priority_array;
    }

    //   ************* Manage Priority Start *****************
    public function get_priority_types(){
        $sql="select * from internal_ticketing_priority order by id desc";
        $result = $this->db_readonly->query($sql)->result();
        return $result;
    }

    public function get_priority_desc($priority){
        $val = $this->db_readonly->select('ip.description as priority_description')
        ->from('internal_ticketing_priority ip')
        ->where('ip.id',$priority)
        ->get()->row();
        return $val;
    }

    public function add_priority(){
        $input = $this->input->post();
        $data = array(
            'priority_name'=>$input['priority_name'],
            'description'=>$input['priority_description'],
            'first_escalation_time'=>$input['first_escalation_time'],
            'second_escalation_time'=>$input['second_escalation_time'],
            'third_escalation_time'=>$input['third_escalation_time']
        );
        return $this->db->insert('internal_ticketing_priority',$data);    
    }

    public function update_priority_status($priority_id){
        $val = $this->db->select('status')
        ->from('internal_ticketing_priority')
        ->where('id',$priority_id)
        ->get()->row();
        $this->db->where('id', $priority_id);
        if ($val->status == 1){  
            $this->db->set('status', 0);
        }else{
            $this->db->set('status', 1);
        }
        return $this->db->update('internal_ticketing_priority');
    } 

    public function edit_priority($priority_id){
        $input = $this->input->post();
       $data = array(
        'description'=>$input['priority_description'],
        'first_escalation_time'=>$input['first_escalation_time'],
        'second_escalation_time'=>$input['second_escalation_time'],
        'third_escalation_time'=>$input['third_escalation_time']
       );
        $this->db->where('id', $priority_id);
        return $this->db->update('internal_ticketing_priority', $data);
    } 

    //   ************* Manage Priority End *****************

    //   ************* Manage Resolution Type Start *****************
    public function get_resolution_types(){
        $sql="select id as resolution_id, resolution_name, status from internal_ticketing_resolution_type order by id desc";
        $result = $this->db_readonly->query($sql)->result();
        return $result;
    }
    public function add_resolution_type($resolution_name){
        $data = array(
            'resolution_name'=>$resolution_name,
        );
        return $this->db->insert('internal_ticketing_resolution_type',$data);    
    }

    public function update_resolution_type_status($resolution_type_id){
        $val = $this->db->select('status')
        ->from('internal_ticketing_resolution_type')
        ->where('id',$resolution_type_id)
        ->get()->row();
        $this->db->where('id', $resolution_type_id);
        if ($val->status == 1){  
            $this->db->set('status', 0);
        }else{
            $this->db->set('status', 1);
        }
        $this->db->update('internal_ticketing_resolution_type');
    } 

    public function get_active_resolution_types(){
        return $this->db->select('id as resolution_id, resolution_name')
        ->from('internal_ticketing_resolution_type')
        ->where('status', 1)
        ->get()->result();
    }

    //   ************* Manage Resolution Type End *****************
    

    //   ************* Manage Issue Type Start *****************
    public function get_issue_types(){
        $data = $this->db_readonly->select("it.*")
        ->from('internal_ticketing_issue_type it')
        ->order_by('it.id', 'desc')
        ->get()->result();

        $first_level_staffs = [];
        foreach ($data as $object) {
            $first_level_assignees = json_decode($object->first_level_assignee);
            // echo "<pre>";print_r($first_level_assignees);die();
            if (!empty($first_level_assignees)) {
                foreach ($first_level_assignees as $key=>$value) {
                    $intValue = intval($value);
                    $result = $this->db_readonly->query("SELECT CONCAT(ifnull(first_name,''),' ', ifnull(last_name,'')) AS staff_name FROM staff_master WHERE id = $intValue")->row();
                    $object->first_level_staffs[] = $result->staff_name;
                }
            } 
            $second_level_staff = $this->db_readonly->query("SELECT CONCAT(ifnull(first_name,''),' ', ifnull(last_name,'')) AS staff_name FROM staff_master WHERE id = $object->second_level_assignee")->row();
            $object->second_level_assignee = $second_level_staff->staff_name;

            $third_level_staff = $this->db_readonly->query("SELECT CONCAT(ifnull(first_name,''),' ', ifnull(last_name,'')) AS staff_name FROM staff_master WHERE id = $object->third_level_assignee")->row();
            $object->third_level_assignee = $third_level_staff->staff_name;

            $department = $this->db_readonly->query("SELECT department AS department_name FROM staff_departments WHERE id = $object->default_department_id")->row();
            $object->department = $department->department_name;
        }
        // echo "<pre>"; print_r($data);die();
        return $data;
    }

    public function get_departments(){
        $sql="select * from staff_departments order by department";
        $result = $this->db->query($sql)->result();
        return $result;
    }

    public function add_issue_type($issue_name, $default_department_id, $first_level_assignee, $second_level_assignee, $third_level_assignee){
        $data = array(
            'issue_name'=>$issue_name,
            'default_department_id'=>$default_department_id,
            'first_level_assignee'=>json_encode($first_level_assignee),
            'second_level_assignee'=>$second_level_assignee,
            'third_level_assignee'=>$third_level_assignee,
            'next_assignee' => (int)$first_level_assignee[0]
        );
        // print_r($data);die();
        return $this->db->insert('internal_ticketing_issue_type',$data);    
    }

    public function get_staff($department_id){
        return $this->db->select("sm.id as staff_id, sm.department, CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) AS staff_name")
        ->from('staff_master sm')
        ->where('sm.status', 2)
        ->order_by("CASE WHEN sm.department ='$department_id' THEN 0 ELSE 1 END, sm.first_name")
        ->get()->result();
    }

    public function get_all_staff(){
        return $this->db->select("sm.id as staff_id, CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) AS staff_name")
        ->from('staff_master sm')
        ->where('sm.status', 2)
        ->order_by('sm.first_name')
        ->get()->result();
    }

    public function get_unassigned_contributors($ticket_id){
        // $result = $this->db_readonly->select("sm.id as staff_id, CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) AS staff_name")
        // ->from('internal_ticket_items_secondary_assignees itsa')
        // ->join('staff_master sm', 'itsa.supportive_assignee=sm.id', 'right')
        // // ->where('itsa.internal_ticketing_item_id', $ticket_id)
        // ->where('itsa.supportive_assignee', NULL)
        // ->get()->result();

        $res = "SELECT sm.id as staff_id, CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) AS staff_name
        FROM staff_master sm
        WHERE sm.status=2 AND
        sm.id NOT IN (
            SELECT itsa.supportive_assignee
            FROM internal_ticket_items_secondary_assignees itsa
            WHERE itsa.internal_ticketing_item_id = $ticket_id
        )
        ORDER BY SUBSTRING_INDEX(sm.first_name, ' ', 1)";
        $result = $this->db_readonly->query($res)->result();
        return $result;
    }

    public function update_issue_type_status($issue_id){
        $val = $this->db->select('status')
        ->from('internal_ticketing_issue_type')
        ->where('id',$issue_id)
        ->get()->row();
        $this->db->where('id', $issue_id);
        if ($val->status == 1){  
            $this->db->set('status', 0);
        }else{
            $this->db->set('status', 1);
        }
        $this->db->update('internal_ticketing_issue_type');
    } 
    //   ************* Manage Issue Type End *****************

    //   ************* Internal ticketing Start *****************
    public function get_issues(){
        return $this->db->select('id as issue_id, issue_name')
        ->from('internal_ticketing_issue_type')
        ->where('status', 1)
        ->get()->result();
    }

    public function get_priorities(){
        return $this->db->select('id as priority_id, priority_name')
        ->from('internal_ticketing_priority')
        ->where('status', 1)
        ->get()->result();
    }

    public function get_departments_in_issue_type_table(){
        return $this->db->select('default_department_id as department_id, department as department_name')
        ->from('internal_ticketing_issue_type ist')
        ->join('staff_departments sd', 'sd.id=ist.default_department_id')
        ->where('ist.status', 1)
        ->group_by('ist.default_department_id')
        ->get()->result();
    }

    public function add_secondary_assignee(){
        $input = $this->input->post();
        
        $data = array(
            'supportive_assignee' => $input['secondary_assignee'],
            'internal_ticketing_item_id' => $input['ticket_id'],
            'created_by' => $input['created_by'],
            'created_on' => $this->Kolkata_datetime()
        );
        return $this->db->insert('internal_ticket_items_secondary_assignees', $data);
    }

    public function get_assigned_staff($issue_type){
        return $this->db_readonly->select("CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) AS assigned_staff, sm.personal_mail_id")
        ->from('internal_ticketing_issue_type ist')
        ->join('staff_master sm', 'ist.next_assignee=sm.id', 'left')
        ->where('ist.id', $issue_type)
        ->get()->row();
    }

    public function get_assigned_staff_id($ticket_id){
        $result = $this->db->select("ist.assigned_to")
        ->from('internal_ticketing_items ist')
        ->where('ist.id', $ticket_id)
        // ->where('ist.issue_type', $issue_type)
        ->get()->row();
        $res_array = [];
        $res_array[] = $result->assigned_to;
        return $res_array;
    }

    public function get_ticket_creator_by_id($ticket_id){
        $result = $this->db_readonly->select("ist.reported_by")
        ->from('internal_ticketing_items ist')
        ->where('ist.id', $ticket_id)
        ->get()->row();
        $res_array = [];
        $res_array[] = $result->reported_by;
        return $res_array;
    }

    public function get_secondary_staff_ids($ticket_id){
        $secondary_assignees = $this->db_readonly->select('sa.supportive_assignee')
        ->from('internal_ticket_items_secondary_assignees sa')
        ->where('sa.internal_ticketing_item_id', $ticket_id)
        ->get()->result();
        $res_array = [];
        if(!empty($secondary_assignees)){
            foreach($secondary_assignees as $secondary_assignee){
                $res_array[] = $secondary_assignee->supportive_assignee;
            }
        }
        return $res_array;
    }

    public function get_staff_by_department($ticket_id){
        $result = $this->db_readonly->select('it.assigned_to as first_level_assignee, ist.second_level_assignee, ist.third_level_assignee')
        ->from('internal_ticketing_items it')
        ->join('internal_ticketing_issue_type ist', 'ist.id=it.issue_type')
        ->where('it.id', $ticket_id)
        ->get()->row();
        $res_array[] = $result->first_level_assignee;
        $res_array[] = $result->second_level_assignee;
        $res_array[] = $result->third_level_assignee;
        return $res_array;
    }

    public function get_ticket_assignee_by_id($ticket_id){
        $result = $this->db_readonly->select("ist.assigned_to")
        ->from('internal_ticketing_items ist')
        ->where('ist.id', $ticket_id)
        ->get()->row();
        $res_array = [];
        $res_array[] = $result->assigned_to;
        return $res_array;
    }

    public function get_ticket_creator_assignee_by_id($ticket_id){
        $result = $this->db_readonly->select("ist.assigned_to, ist.reported_by")
        ->from('internal_ticketing_items ist')
        ->where('ist.id', $ticket_id)
        ->get()->row();
        $res_array = [];
        $res_array[] = $result->assigned_to;
        $res_array[] = $result->reported_by;
        return $res_array;
    }

    public function insert_ticket_data($title, $paths, $issue_type, $priority, $description, $created_by, $reported_by, $reported_by_name, $confidentiality){
        $input = $this->input->post();
        
        $issue = $input['issue_type'];
        $issue_type_res = $this->db->select("ist.default_department_id, ist.next_assignee, CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) AS assigned_staff")
        ->from('internal_ticketing_issue_type ist')
        ->join('staff_master sm', 'sm.id=ist.next_assignee')
        ->where('ist.id', $issue)
        ->get()->row();

        $this->db->trans_start();
        $data = array(
            'title' => $title,
            'reported_by' => $reported_by,
            'description' => $description,
            'ticket_status' => 'Open', 
            'resolution_remarks' => NULL, 
            'assigned_to' =>  $issue_type_res->next_assignee,
            'issue_type' => $issue_type,
            'priority' => $priority,
            'assigned_to_department_id' => $issue_type_res->default_department_id, 
            'created_by' => $created_by, 
            'created_on' => $this->Kolkata_datetime(),
            'escalation_status' => 'No Escalation',
            'confidentiality' => $confidentiality
        );
        $this->db->insert('internal_ticketing_items', $data);
        $ticket_id = $this->db->insert_id();

        $ticketItemData =array(
            'ticket_item_id' => 'T' . str_pad($ticket_id, 6, '0', STR_PAD_LEFT)
        );
        $this->db->where('id', $ticket_id);
        $this->db->update('internal_ticketing_items',$ticketItemData);

        if (!empty($paths)){
            $this->insert_ticket_attachments($ticket_id, $paths);
        }

        //Update next assignee
        $this->update_next_assignee($issue_type);

        //Update ticket history
        if ($created_by != $reported_by) {
            $action = "Ticket Created on behalf of $reported_by_name";
        } else {
            $action = 'Ticket Reported';
        }
        $this->update_history($ticket_id, $created_by, $action);
        $action = "Ticket Assigned to $issue_type_res->assigned_staff";
        $this->update_history($ticket_id, 0, $action);
        $this->db->trans_complete();

        if ($this->db->trans_status()) {
            return $ticket_id;
        } else {
            return 0;
        }
    }

    public function update_next_assignee($issue_type){
        $val = $this->db_readonly->select('ist.first_level_assignee, ist.next_assignee as current_assignee_from_table')
        ->from('internal_ticketing_issue_type ist')
        ->where('ist.id', $issue_type)
        ->get()->row();
        
        $first_level_assignees = json_decode($val->first_level_assignee);
        $lastIndex = count($first_level_assignees) - 1;
        $current_assignee_index = array_search($val->current_assignee_from_table, $first_level_assignees);
        if ($current_assignee_index == $lastIndex){
            $nextAssignee = (int)$first_level_assignees[0];
        }else{
            $next_index = ($current_assignee_index + 1) % count($first_level_assignees); 
            $nextAssignee = (int)$first_level_assignees[$next_index];
        }
        $data=array(
            'next_assignee' => $nextAssignee
        );
        $this->db->where('id', $issue_type);
        $this->db->update('internal_ticketing_issue_type',$data);
        return true;
    }


    public function get_email_template(){
        return $this->db->where('name','internal ticketing')->get('email_template')->row();
    }

    public function insert_ticket_attachments($ticket_id, $paths) {
        if($paths){
            foreach ($paths as $k => $path) {
                $files_data[] = array(
                    'it_id' => $ticket_id,
                    'file_path' => $path['path'],
                );
            }
        }else{
            $files_data[] = array(
                'it_id' => $ticket_id
            );
        }
        $this->db->insert_batch('internal_ticketing_attachments', $files_data);
    }

    public function ticket_details_by_id($ticket_id){
        $data = $this->db_readonly->select("it.title, it.description,  date_format(it.created_on,'%d-%b-%Y %h:%i %p') as created_on,
        it.ticket_status, it.resolution_remarks as resolution_remarks,
        it.assigned_to,
        date_format(it.due_date,'%d-%b-%Y') as due_date,
        it.escalation_status,
        issue.issue_name as issue_name, 
        issue.id as issue_type_id, 
        p.priority_name as priority_name,
        sd.department as department_name,
        CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) AS assigned_staff,
        CONCAT(ifnull(sm_1.first_name,''),' ', ifnull(sm_1.last_name,'')) AS created_by,
        CONCAT(ifnull(sm_2.first_name,''),' ', ifnull(sm_2.last_name,'')) AS reported_by,
        it.assigned_to_department_id, it.id as ticket_id, it.ticket_item_id,issue_type")
                ->from('internal_ticketing_items it')
                ->join('internal_ticketing_issue_type issue', 'it.issue_type=issue.id', 'left')
                ->join('internal_ticketing_priority p', 'it.priority=p.id', 'left')
                ->join('staff_master sm', 'it.assigned_to=sm.id', 'left')
                ->join('staff_master sm_1', 'it.created_by=sm_1.id', 'left')
                ->join('staff_master sm_2', 'it.reported_by=sm_2.id', 'left')
                ->join('staff_departments sd','sd.id=it.assigned_to_department_id')
                ->where('it.id', $ticket_id)->get()->row();

        $files = $this->db_readonly
        ->select("file_path")
        ->from("internal_ticketing_attachments")
        ->where("it_id", $ticket_id)
        ->get()->result();
            
        $document = [];
        foreach ($files as $file) {
            $document[] = $this->filemanager->getFilePath($file->file_path);
        }
        $data->doc = $document;

        $history = $this->db_readonly
        ->select("ih.id as history_id, ih.action, date_format(ih.created_on,'%d-%b-%Y %H:%i') as history_updated_on,
        CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) AS history_updated_by")
        ->from("internal_ticketing_history ih")
        ->join('internal_ticketing_items it', 'ih.internal_ticketing_items_id=it.id', 'left')
        ->join('staff_master sm', 'ih.created_by=sm.id', 'left')
        ->where('it.id', $ticket_id)
        ->order_by('ih.id', 'desc')
        ->get()->result();
        $data->history = $history;
        // print_r($data->history);

        $assigned_staff_department_id = $this->db_readonly->select('it.assigned_to_department_id')
        ->from("internal_ticketing_items it")
        ->where("it.id", $ticket_id)
        ->get()->row();
        
        // $staff_data =  $this->db_readonly->select("sm.id as staff_id, CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) AS staff_name")
        // ->from("staff_master sm")
        // // ->where("sm.department", $assigned_staff_department_id->assigned_to_department_id)
        // ->where("sm.status", 2)
        // ->order_by("sm.first_name")
        // ->get()->result();
        // $data->staff_data = $staff_data;

        $res = "SELECT sm.id as staff_id, CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) AS staff_name
        FROM staff_master sm
        WHERE sm.status=2 AND
        sm.id NOT IN (
            SELECT it.assigned_to
            FROM internal_ticketing_items it
            WHERE it.id = $ticket_id
        )
        ORDER BY SUBSTRING_INDEX(sm.first_name, ' ', 1)";
        $staff_data = $this->db_readonly->query($res)->result();
        $data->staff_data = $staff_data;

        $issue_type_data = $this->db_readonly->select("issue_type.id as issue_type_id, issue_type.issue_name as issue_type_name")
        ->from("internal_ticketing_issue_type issue_type")
        ->where("issue_type.status", 1)
        ->get()->result();
        $data->issue_type_data = $issue_type_data;

        $secondary_staffs = $this->db_readonly->select("CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) AS staff_name, sm.id as staff_id")
        ->from("internal_ticket_items_secondary_assignees itsa")
        ->join("staff_master sm", "sm.id=itsa.supportive_assignee")
        ->where("itsa.internal_ticketing_item_id", $ticket_id)
        ->get()->result();
        $data->secondary_staffs = $secondary_staffs;

        $logged_in_user = $this->authorization->getAvatarStakeHolderId();

        $logged_in_user_department = $this->db_readonly->select("sm.department")
        ->from("staff_master sm")
        ->where("sm.id", $logged_in_user)
        ->get()->row();

        $logged_in_staff = '';
        if (!empty($logged_in_user_department) && $assigned_staff_department_id->assigned_to_department_id == $logged_in_user_department->department){
            $logged_in_staff = 'Yes';
        }else{
            $logged_in_staff  = 'No';
        }
        $data->logged_in_staff = $logged_in_staff;
        return $data;
    }

    public function get_history_by_ticket_id($ticket_id){
        $history = $this->db_readonly
        ->select("ih.id as history_id, ih.action, date_format(ih.created_on,'%d-%b-%Y %H:%i') as history_updated_on,
        CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) AS history_updated_by")
        ->from("internal_ticketing_history ih")
        ->join('internal_ticketing_items it', 'ih.internal_ticketing_items_id=it.id', 'left')
        ->join('staff_master sm', 'ih.created_by=sm.id', 'left')
        ->where('it.id', $ticket_id)
        ->order_by('ih.id', 'desc')
        ->get()->result();

        return $history;
    }

    public function get_close_reopen_comments($ticket_id){
        $data = $this->db_readonly->select("it.title, it.ticket_status, date_format(it.closed_on,'%d-%b-%Y %H:%i') as closed_on, it.closure_remarks, it.closure_rating,
        CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) AS closed_by,
        it.id as ticket_id, it.ticket_item_id")
        ->from('internal_ticketing_items it')
        ->join('staff_master sm', 'it.closed_by=sm.id', 'left')
        ->where('it.id', $ticket_id)
        ->get()->row();
        return $data;
    }

    public function unassigned_staffs($ticket_id){
        $res = "SELECT sm.id as staff_id, CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) AS staff_name
        FROM staff_master sm
        WHERE sm.status=2 AND
        sm.id NOT IN (
            SELECT it.assigned_to
            FROM internal_ticketing_items it
            WHERE it.id = $ticket_id
        )
        ORDER BY SUBSTRING_INDEX(sm.first_name, ' ', 1)";
        $staff_data = $this->db_readonly->query($res)->result();
        // $data->staff_data = $staff_data;
        return $staff_data;
    }

    public function remove_contributor($ticket_id, $staff_id){
        $this->db->where('internal_ticketing_item_id',$ticket_id);
        $this->db->where('supportive_assignee',$staff_id);
        return $this->db->delete('internal_ticket_items_secondary_assignees');
    }
     
    public function update_ticket_assignee($ticket_id, $selected_staff){
        $initial_assignee = $this->db_readonly->select("CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) AS staff_name")
        ->from("internal_ticketing_items it")
        ->join("staff_master sm", "sm.id=it.assigned_to")
        ->where("it.id", $ticket_id)
        ->get()->row();

        $current_staff_name = $this->db_readonly->select("CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) AS staff_name")
        ->from("staff_master sm")
        ->where("sm.id", $selected_staff)
        ->get()->row();

        $updated_by = $this->authorization->getAvatarStakeHolderId();
        $this->db->trans_start();
        $data=array(
            'assigned_to' => $selected_staff
        );
        $this->db->where('id', $ticket_id);
        $this->db->update('internal_ticketing_items',$data);
        $action = "Reassigned ticket from {$initial_assignee->staff_name} to {$current_staff_name->staff_name}";
        if($this->db->trans_complete()){
            $this->db->trans_start();
            $this->update_history($ticket_id, $updated_by, $action);
            return $this->db->trans_complete();
        }
        else{
            return false;
        } 
    }

    public function update_ticket_issue_type($ticket_id, $selected_issue_type){
        $initial_issue_type = $this->db_readonly->select("ist.issue_name")
        ->from("internal_ticketing_items it")
        ->join("internal_ticketing_issue_type ist", "ist.id=it.issue_type")
        ->where("it.id", $ticket_id)
        ->get()->row();

        $current_issue_type = $this->db_readonly->select("ist.issue_name, ist.default_department_id, ist.next_assignee as first_level_assignee")
        ->from("internal_ticketing_issue_type ist", "ist.id=it.issue_type")
        ->where("ist.id", $selected_issue_type)
        ->get()->row();

        $updated_by = $this->authorization->getAvatarStakeHolderId();
        $this->db->trans_start();
        $data=array(
            'issue_type' => $selected_issue_type, 
            'assigned_to_department_id' => $current_issue_type->default_department_id,
            'assigned_to' =>  $current_issue_type->first_level_assignee
        );
        $this->db->where('id', $ticket_id);
        $this->db->update('internal_ticketing_items',$data);
        $action = "Reassigned ticket from {$initial_issue_type->issue_name} to {$current_issue_type->issue_name}";
        if($this->db->trans_complete()){
            $this->db->trans_start();
            $this->update_history($ticket_id, $updated_by, $action);
            return $this->db->trans_complete();
        }
        else{
            return false;
        } 
    }

    public function update_ticket_progress($ticket_id, $due_date){
        $updated_by = $this->authorization->getAvatarStakeHolderId();
        $this->db->trans_start();
        $data=array(
            'ticket_status' => 'In Progress',
            'due_date' => $due_date,
            'in_progress_marked_time' => $this->Kolkata_datetime(),
            'in_progress_by' => $updated_by
        );
        $this->db->where('id', $ticket_id);
        $this->db->update('internal_ticketing_items',$data);
        $action = 'Marked as In Progress';
        $due_date_action = "Due date entered as $due_date";
        if($this->db->trans_complete()){
            $this->db->trans_start();
            $this->update_history($ticket_id, $updated_by, $action);
            $this->update_history($ticket_id, $updated_by, $due_date_action);
            return $this->db->trans_complete();
        }
        else{
            return false;
        } 
    }

    public function get_ticket_details($issue, $priority, $department, $status, $reported_by_me, $assigned_to_me, $filter_type){
        // print_r($assigned_to_me);die();
        $logged_in_staff = $this->authorization->getAvatarStakeHolderId();
        $logged_in_staff_department = $this->db_readonly->select('sm.department')
        ->from('staff_master sm')
        ->where('sm.id', $logged_in_staff)
        ->get()->row();

        $this->db_readonly->select("it.assigned_to, it.ticket_status, it.title, it.ticket_item_id, it.id as ticketid, it.confidentiality, ifnull(it.resolution_remarks,'') as resolution_remarks, date_format(it.created_on,'%d-%b %h:%i %p') as created_on, p.priority_name, CONCAT(ifnull(sm_1.first_name,''),' ', ifnull(sm_1.last_name,'')) AS reported_by_name, it.reported_by as reported_by_id, it.assigned_to_department_id, ist.supportive_assignee");
        $this->db_readonly->from('internal_ticketing_items it');
        $this->db_readonly->join('internal_ticketing_issue_type issue', 'it.issue_type=issue.id', 'left');
        $this->db_readonly->join('internal_ticketing_priority p', 'it.priority=p.id', 'left');
        $this->db_readonly->join('staff_master sm_1', 'it.reported_by=sm_1.id', 'left');
        $this->db_readonly->join('internal_ticket_items_secondary_assignees ist', 'it.id=ist.internal_ticketing_item_id', 'left');

        if ($issue){
            $this->db_readonly->where_in('issue.id', $issue);
        }
        if ($priority){
            $this->db_readonly->where_in('p.id', $priority);
        }
        if ($department){
            $this->db_readonly->where_in('it.assigned_to_department_id', $department);
        }
        if ($filter_type == 'all_open_tickets'){
            $this->db_readonly->where_in('it.ticket_status', ['Open', 'In Progress', 'Reopen']);
        } else if ($status){
            $this->db_readonly->where_in('it.ticket_status', $status);
            $this->db_readonly->order_by('it.ticket_status');
        }
        if ($reported_by_me){
            $this->db_readonly->where_in('it.reported_by', $logged_in_staff);
        }
        if ($assigned_to_me){
            $this->db_readonly->where('it.assigned_to', $logged_in_staff);
            $this->db_readonly->or_where('ist.supportive_assignee', $logged_in_staff);
        }
        if ($filter_type == 'reported_by_me'){
            $this->db_readonly->where_in('it.reported_by', $logged_in_staff);
        }
        if ($filter_type == 'open_tickets_assigned_to_me'){
            $this->db_readonly->where_in('it.ticket_status', ['Open', 'In Progress', 'Reopen']);
            $this->db_readonly->where_in('it.assigned_to', $logged_in_staff);
            $this->db_readonly->or_where('ist.supportive_assignee', $logged_in_staff);
        }
        if ($filter_type == 'closed_by_me'){
            $this->db_readonly->where_in('it.closed_by', $logged_in_staff);
        }
        $this->db_readonly->order_by('it.id', 'desc');
        $result =  $this->db_readonly->get()->result();

        //Filtering out confidential tickets
        $new_result = [];
        foreach($result as $ticket_obj){
            if($ticket_obj->confidentiality == 0){
                $new_result[] = $ticket_obj;
            } else {
                //Confidential tickets
                if ($ticket_obj->assigned_to == $logged_in_staff || $ticket_obj->reported_by_id == $logged_in_staff){
                    $new_result[] = $ticket_obj;
                }
                else if ($ticket_obj->supportive_assignee == $logged_in_staff  || $ticket_obj->reported_by_id == $logged_in_staff){
                    $new_result[] = $ticket_obj;
                }
            }
        }
        //If the user has Admin permissions, no need to check for access control
        $admin_permission = $this->authorization->isAuthorized('INTERNAL_TICKETING.INTERNAL_TICKETING_ADMIN');
        if($admin_permission){
            return $new_result;
        }

        //Filtering for access control
        $access_control = $this->settings->getSetting('access_control_for_internal_ticketing');
        if ($access_control == "0"){
            return $new_result;
        }
        $final_result = [];
        foreach($new_result as $final_ticket_obj){
            if($final_ticket_obj->assigned_to == $logged_in_staff  || $final_ticket_obj->reported_by_id == $logged_in_staff || $final_ticket_obj->assigned_to_department_id == $logged_in_staff_department->department){
                $final_result[] = $final_ticket_obj;
            }
            else if ($final_ticket_obj->supportive_assignee == $logged_in_staff || $final_ticket_obj->reported_by_id == $logged_in_staff || $final_ticket_obj->assigned_to_department_id == $logged_in_staff_department->department){
                $final_result[] = $final_ticket_obj;
            }
        }
        return $final_result;
    }

    public function update_resolution_details($ticket_id, $resolution_remarks, $ticket_status, $nature_of_resolution, $resolved_by){
        $data=array(
            'resolution_remarks'=> $resolution_remarks,
            'ticket_status' => $ticket_status,
            'nature_of_resolution' => $nature_of_resolution,
            'resolved_by' => $resolved_by,
            'resolved_on' => $this->Kolkata_datetime()
        );
        $this->db->where('id', $ticket_id);
        $this->db->update('internal_ticketing_items',$data);
        $this->db->trans_start();
        $action = "Resolved with remarks: $resolution_remarks";
        if($this->db->trans_complete()){
            $this->db->trans_start();
            $this->update_history($ticket_id, $resolved_by, $action);
            return $this->db->trans_complete();
        }
        else{
            return false;
        } 
    }

    public function close_reopen_ticket($ticket_id, $closure_remarks, $ticket_status, $closed_by, $closure_rating){
        $data=array(
            'closure_remarks'=> $closure_remarks,
            'ticket_status' => $ticket_status,
            'closed_by' => $closed_by,
            'closure_rating' => $closure_rating,
            'closed_on' => $this->Kolkata_datetime()
        );
        $this->db->where('id', $ticket_id);
        $this->db->update('internal_ticketing_items',$data);
        $this->db->trans_start();
        $action = "$ticket_status with remarks: $closure_remarks";
        if($this->db->trans_complete()){
            $this->db->trans_start();
            $this->update_history($ticket_id, $closed_by, $action);
            return $this->db->trans_complete();
        }
        else{
            return false;
        } 
    }

    public function get_ticket_status($ticket_id){
        $this->db_readonly->select("it.ticket_status as ticket_status, it.resolution_remarks as resolution_comments, itrt.resolution_name as nature_of_resolution, CONCAT(ifnull(sm_1.first_name,''),' ', ifnull(sm_1.last_name,'')) AS resolved_by, date_format(it.resolved_on,'%d-%b-%Y %H:%i') as resolved_on, it.assigned_to, it.closure_remarks, date_format(it.closed_on,'%d-%b-%Y %H:%i') as closed_on, CONCAT(ifnull(sm_2.first_name,''),' ', ifnull(sm_2.last_name,'')) AS closed_by, it.reported_by, it.assigned_to, it.id as ticket_id, it.closure_rating");
        $this->db_readonly->from('internal_ticketing_items it');
        $this->db_readonly->join('staff_master sm_1', 'it.resolved_by=sm_1.id', 'left');
        $this->db_readonly->join('staff_master sm_2', 'it.closed_by=sm_2.id', 'left');
        $this->db_readonly->join('internal_ticketing_resolution_type itrt', 'it.nature_of_resolution=itrt.id', 'left');
        $this->db_readonly->where('it.id', $ticket_id);
        return $this->db_readonly->get()->row();
    }

    public function get_priority_details($ticket_id){
        $val = $this->db_readonly->select('ip.*')
        ->from('internal_ticketing_items it')
        ->join('internal_ticketing_priority ip', 'ip.id=it.priority')
        ->where('it.id',$ticket_id)
        ->get()->row();
        return $val;
    }

    public function get_resolution_details($ticket_id){
        $this->db_readonly->select("*, itrt.resolution_name, CONCAT(ifnull(sm_1.first_name,''),' ', ifnull(sm_1.last_name,'')) AS resolved_by, it.resolution_remarks as resolution_comments, date_format(it.resolved_on,'%d-%b-%Y %H:%i') as resolved_on");
        $this->db_readonly->from('internal_ticketing_items it');
        $this->db_readonly->join('staff_master sm_1', 'it.resolved_by=sm_1.id', 'left');
        $this->db_readonly->join('internal_ticketing_resolution_type itrt', 'it.nature_of_resolution=itrt.id', 'left');
        $this->db_readonly->where('it.id', $ticket_id);
        return $this->db_readonly->get()->row();
    }

    #*********************Comments************************

    public function get_comments($ticket_id){
        // echo $ticket_id;  die();
        // $this->db_readonly->select("itc.comments as ticket_comments, CONCAT(ifnull(sm_1.first_name,''),' ', ifnull(sm_1.last_name,'')) AS commented_by, date_format(itc.commented_on,'%d-%b-%Y %H:%i') as commented_on, itc.id as comment_id");
        // $this->db_readonly->from('internal_ticketing_items_comments itc');
        // // $this->db_readonly->join('internal_ticketing_comments_attachments itca', 'itc.id=itca.comment_id');
        // // $this->db_readonly->join('internal_ticketing_items it', 'it.id=itc.internal_ticketing_items_id');
        // $this->db_readonly->join('staff_master sm_1', 'itc.commented_by=sm_1.id', 'left');
        // $this->db_readonly->where('itc.internal_ticketing_items_id', $ticket_id);
        // $this->db_readonly->order_by('itc.id', 'desc');
        // $data = $this->db_readonly->get()->result();

        // $files = $this->db_readonly
        // ->select("itca.file_path, itca.comment_id as it_comment_id")
        // ->from("internal_ticketing_comments_attachments itca")
        // ->join('internal_ticketing_items_comments itc', 'itc.id=itca.comment_id')
        // ->where("itc.internal_ticketing_items_id", $ticket_id)
        // ->get()->result();
        // $document = [];
        // foreach ($data as $ticket){
        //     foreach ($files as $file){
        //         // print_r(typeof($file));die();
        //         if ($file->file_path != null){
        //             if ($ticket->comment_id == $file->it_comment_id){
        //                 $document[] = $this->filemanager->getFilePath($file->file_path);
        //                 $ticket->doc = $document;
        //             }
        //         }else{
        //             $ticket->doc = [];
        //         }
        //     }
        // }
        // return $data;

        $internal_ticketing_query="SELECT itc.id AS inetrnal_ticket_id, iti_c.id AS comment_id, comments AS ticket_comment ,  CONCAT(ifnull(sm_1.first_name,''),' ', ifnull(sm_1.last_name,''))  AS commented_by, date_format(iti_c.commented_on,'%d-%b-%Y %h:%i %p') AS commented_on
        FROM internal_ticketing_items_comments iti_c
        JOIN internal_ticketing_items itc ON itc.id=iti_c.internal_ticketing_items_id
        LEFT JOIN  staff_master sm_1 ON sm_1.id=iti_c.commented_by
        WHERE itc.id=$ticket_id
        ORDER BY iti_c.id DESC";
        
        $internal_ticketing_query=$this->db_readonly->query($internal_ticketing_query)->result();
        foreach($internal_ticketing_query as $key => $val){
            $val->file_paths=$this->db_readonly->query("select file_path from internal_ticketing_comments_attachments where comment_id=$val->comment_id")->result();

            foreach($val->file_paths as $key => $val2){
                if($val2->file_path)
                    $val2->file_path=$this->filemanager->getFilePath($val2->file_path);
            }
        }

        return $internal_ticketing_query;
    }

    public function insert_comment(){
        $input = $this->input->post();
        // print_r($input);die();
        $data = array(
            'comments' => $input['comments'], 
            'internal_ticketing_items_id' =>  $input['ticket_id'],
            'commented_by' => (isset($input['commented_by']) =='')? NULL : $input['commented_by'], 
            'commented_on' => $this->Kolkata_datetime()
        );
        // echo '<pre>';print_r($data);die();
        $this->db->trans_start();
        $this->db->insert('internal_ticketing_items_comments',$data);
        $comment_id = $this->db->insert_id();
        $paths = $input['file_path']=='null'?null:$input['file_path'];
        if($this->db->trans_complete()){
            $this->db->trans_start();
            $this->insert_comment_attachments($comment_id, $paths);
        }
        else{
            return false;
        } 
        // print_r($paths);die();
        $ticket_id = $input['ticket_id'];
        $created_by = $input['commented_by'];
        $action = 'Comment';
        if($this->db->trans_complete()){
            $this->db->trans_start();
            $this->update_history($ticket_id, $created_by, $action);
            return $this->db->trans_complete();
        }
        else{
            return false;
        } 
    }

    public function insert_comment_attachments($comment_id, $paths) {
        if($paths){
            foreach ($paths as $k => $path) {
                $files_data[] = array(
                    'comment_id' => $comment_id,
                    'file_path' => $path['path'],
                );
            }
        }
        else{
            $files_data[] = array(
                'comment_id' => $comment_id
            );
        }

        $this->db->trans_start();
        $this->db->insert_batch('internal_ticketing_comments_attachments', $files_data);
        return $this->db->trans_complete();
    }

    public function update_history($ticket_id, $created_by, $action){
        $data = array(
            'internal_ticketing_items_id' =>  $ticket_id,
            'created_by' => $created_by, 
            'created_on' => $this->Kolkata_datetime(),
            'action' => $action
        );
        // echo '<pre>';print_r($data);die();
        return $this->db->insert('internal_ticketing_history',$data);
    }

    public function get_assigned_internal_ticket_staff_id($interntal_ticket_last_id){
        $res =  $this->db->select("u.email, sm.id as staff_id, iti.ticket_item_id, CONCAT(ifnull(sm_1.first_name,''),' ', ifnull(sm_1.last_name,'')) AS reported_by, iti.title, iti.description, ip.priority_name, ii.issue_name, sd.department as department_name, iti.escalation_status")
        ->from('internal_ticketing_items iti')
        ->where('iti.id',$interntal_ticket_last_id)
        ->join('staff_master sm','iti.assigned_to=sm.id', 'left')
        ->join('internal_ticketing_priority ip', 'ip.id=iti.priority')
        ->join('internal_ticketing_issue_type ii', 'ii.id=iti.issue_type')
        ->join('staff_departments sd', 'sd.id=iti.assigned_to_department_id')
        ->join('staff_master sm_1','iti.reported_by=sm_1.id', 'left')
        ->join('avatar a', 'a.stakeholder_id=sm.id')
        ->join('users u', 'a.user_id=u.id')
        ->where('a.avatar_type', '4')
        ->where('sm.status', 2)
        ->get()->row();
        return $res;
    }

    public function get_ticket_details_for_notification($ticket_id){
        $res=  $this->db_readonly->select("it.ticket_item_id, it.id as ticket_id, it.reported_by, CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) AS created_staff_name, CONCAT(ifnull(sm_1.first_name,''),' ', ifnull(sm_1.last_name,'')) AS assigned_to_staff_name, it.assigned_to, it.resolved_by, it.in_progress_by, it.closed_by, sd.department as department_name, u.email as created_by_staff_email, it.title, it.description, ip.priority_name, ii.issue_name, it.escalation_status")
        ->from('internal_ticketing_items it')
        ->join('staff_departments sd','sd.id=it.assigned_to_department_id', 'left')
        ->join('staff_master sm',"sm.id=it.reported_by", 'left')
        ->join('staff_master sm_1',"sm.id=it.assigned_to", 'left')
        ->join('internal_ticketing_priority ip', 'ip.id=it.priority')
        ->join('internal_ticketing_issue_type ii', 'ii.id=it.issue_type')
        ->join('avatar a', 'a.stakeholder_id=sm.id')
        ->join('users u', 'a.user_id=u.id')
        ->where('a.avatar_type', '4')
        ->where('it.id', $ticket_id)
        ->get()->row();
        return $res;
    }

    public function get_department_staff_emails($ticket_id){
        $data = $this->db_readonly->select("it.ticket_item_id, ist.*")
        ->from('internal_ticketing_items it')
        ->join('internal_ticketing_issue_type ist', 'it.issue_type=ist.id', 'left')
        ->where('it.id', $ticket_id)
        ->get()->row();

        $first_level_staffs = [];
        $first_level_staff_array=[];
        $second_level_staff_array=[];
        $third_level_staff_array=[];

        // foreach ($data as $object) {
            $first_level_assignees = json_decode($data->first_level_assignee);
            // echo "<pre>";print_r($first_level_assignees);die();
            if (!empty($first_level_assignees)) {
                foreach ($first_level_assignees as $key=>$value) {
                    // print_r($value);

                    $intValue = intval($value);
                    $result = $this->db_readonly->query("SELECT CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) AS staff_name, sm.id as staff_id, u.email 
                    FROM staff_master sm 
                    JOIN avatar a ON a.stakeholder_id=sm.id
                    JOIN users u ON a.user_id=u.id
                    WHERE a.avatar_type='4' AND sm.id = $intValue")->row();
                    $first_level_staff_array[$result->staff_id] = $result->email;
                    // $value[$key] = $result->email;
                    // $value[$key] = $result->staff_id;
                    // $data->first_level_staffs[] = $result->staff_id;
                }
            } 
            // print_r($first_level_staff_array);

            // die();
            $second_level_staff = $this->db_readonly->query("SELECT CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) AS staff_name, sm.id as staff_id, u.email FROM staff_master sm
            JOIN avatar a ON a.stakeholder_id=sm.id
            JOIN users u ON a.user_id=u.id
            WHERE a.avatar_type='4' AND sm.id = $data->second_level_assignee")->row();
            // $data->second_level_assignee = 
            // $second_level_staff_array[$second_level_staff->staff_id] = $second_level_staff->email;
            $first_level_staff_array[$second_level_staff->staff_id] = $second_level_staff->email;


            $third_level_staff = $this->db_readonly->query("SELECT CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) AS staff_name, sm.id as staff_id, u.email FROM staff_master sm 
            JOIN avatar a ON a.stakeholder_id=sm.id
            JOIN users u ON a.user_id=u.id
            WHERE a.avatar_type='4' AND sm.id = $data->third_level_assignee")->row();
            // $data->third_level_assignee = $third_level_staff->email;
            $first_level_staff_array[$third_level_staff->staff_id] = $third_level_staff->email;

            // $third_level_staff_array[$third_level_staff->staff_id] = $third_level_staff->email; 

        // $mergedArray = array_merge($first_level_staff_array, $second_level_staff_array, $third_level_staff_array);
            $data->staff_data = $first_level_staff_array;

        return $data;
    }

    public function get_secondary_staff_emails($ticket_id){
        $secondary_assignees = $this->db_readonly->select('sa.supportive_assignee')
        ->from('internal_ticket_items_secondary_assignees sa')
        ->where('sa.internal_ticketing_item_id', $ticket_id)
        ->get()->result();
        $secondary_staff_array = [];
        if(!empty($secondary_assignees)){
            foreach($secondary_assignees as $secondary_assignee){
                $result = $this->db_readonly->query("SELECT CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) AS staff_name, sm.id as staff_id, u.email 
                FROM staff_master sm 
                JOIN avatar a ON a.stakeholder_id=sm.id
                JOIN users u ON a.user_id=u.id
                WHERE a.avatar_type='4' AND sm.id = $secondary_assignee->supportive_assignee")->row();
                $secondary_staff_array[$result->staff_id] = $result->email;
            }
        }
        $data = new stdClass();
        $data->staff_data = $secondary_staff_array;
        return $data;
    }


    public function get_staff_name_and_email($ticket_id, $type){
        return $result = $this->db_readonly->select("it.$type, CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) AS staff_name, u.email")
        ->from('internal_ticketing_items it')
        ->join('staff_master sm',"sm.id=it.$type", 'left')
        ->join('avatar a', 'a.stakeholder_id=sm.id')
        ->join('users u', 'a.user_id=u.id')
        ->where('a.avatar_type', '4')
        ->where('it.id', $ticket_id)
        ->get()->row();
    }

    public function get_staff_details_for_notification($ticket_id, $action_type){
        $result = $this->db_readonly->select("it.ticket_item_id, it.reported_by, it.assigned_to, it.resolved_by, it.in_progress_by, it.closed_by, sd.department as department_name")
        ->from('internal_ticketing_items it')
        ->join('staff_departments sd','sd.id=it.assigned_to_department_id', 'left')
        ->where('it.id', $ticket_id)
        ->get()->row();
        if ($action_type == 'Created'){
            $val = $this->staff_detail($result->reported_by);
            $result->staff_name = $val->staff_name;
            $result->email = $val->email;
        }else if($action_type == 'Assignee'){
            $val = $this->staff_detail($result->assigned_to);
            $result->staff_name = $val->staff_name;
            $result->email = $val->email;
        }else if($action_type == 'Resolved'){
            $val = $this->staff_detail($result->resolved_by);
            $result->staff_name = $val->staff_name;
            $result->email = $val->email;
        }else if($action_type == 'Closed' OR $action_type == 'Reopened'){
            $val = $this->staff_detail($result->closed_by);
            $result->staff_name = $val->staff_name;
            $result->email = $val->email;
        }
        return $result;
    }


    public function staff_detail($staff_id){
        $res = $this->db_readonly->select("CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) AS staff_name, u.email")
        ->from('staff_master sm')
        ->join('avatar a', 'a.stakeholder_id=sm.id')
        ->join('users u', 'a.user_id=u.id')
        ->where('a.avatar_type', '4')
        ->where('sm.status', 2)
        ->where('sm.id', $staff_id)
        ->get()->row();
        $res->staff_name = $res->staff_name;
        $res->email = $res->email;
        // if ($return_type == 'staff_name'){
        //     return $res->staff_name;
        // } else{
        //     return $res->email;
        // }
        return $res;
    }


    public function get_all_tickets_with_escalation_time(){
        $val = $this->db->select('it.id as ticket_id, it.created_on, ip.first_escalation_time, ip.second_escalation_time, ip.third_escalation_time, it.ticket_status, it.escalation_status, it.assigned_to, ist.second_level_assignee, ist.third_level_assignee, it.reported_by, it.ticket_item_id')
        ->from('internal_ticketing_items it')
        ->join('internal_ticketing_priority ip', 'it.priority=ip.id')
        ->join('internal_ticketing_issue_type ist', 'it.issue_type=ist.id')
        ->where('it.ticket_status', 'Open')
        ->get()->result();

        return $val;
    }

    public function update_escalation_status_and_assignee($ticket_id, $status){
        switch ($status) {
            case 'First Escalation':
                $data = array(
                    'itt.escalation_status'=>$status
                );
                break;
            case 'Second Escalation':
                $get_second_level_staff = $this->db->select('ist.second_level_assignee')
                ->from('internal_ticketing_items it')
                ->join('internal_ticketing_issue_type ist','it.issue_type=ist.id')
                ->where('it.id', $ticket_id)
                ->get()->row();
                $data = array(
                    'itt.escalation_status'=>$status,
                    'itt.assigned_to'=>$get_second_level_staff->second_level_assignee
                );
                break;
            case 'Third Escalation':
                $get_third_level_staff = $this->db->select('ist.third_level_assignee')
                ->from('internal_ticketing_items it')
                ->join('internal_ticketing_issue_type ist','it.issue_type=ist.id')
                ->where('it.id', $ticket_id)
                ->get()->row();
                $data = array(
                    'itt.escalation_status'=>$status,
                    'itt.assigned_to'=>$get_third_level_staff->third_level_assignee
                );
                break;
            default:
              break;
        }
        $this->db->where('itt.id', $ticket_id);
        $this->db->update('internal_ticketing_items itt', $data);

        $escalated_to = $this->db->select("it.assigned_to, CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) AS assigned_to_staff")
        ->from('internal_ticketing_items it')
        ->join('staff_master sm', 'sm.id=it.assigned_to', 'left')
        ->where('it.id', $ticket_id)
        ->get()->row();

        $action = "Ticket Escalated to $escalated_to->assigned_to_staff";
        return $this->update_history($ticket_id, 0, $action);
    }


    public function get_department_wise_ticket_count(){
        $departments = $this->db_readonly->select("it.assigned_to_department_id as department_id, sd.department")
        ->from('internal_ticketing_items it')
        ->join('staff_departments sd', 'sd.id=it.assigned_to_department_id')
        ->get()->result();

        foreach ($departments as $key) {
            $result = $this->db_readonly->select("count(it.id) as count")
            ->from('internal_ticketing_items it')
            ->where('it.assigned_to_department_id', $key->department_id)
            ->get()->row();
            $department_array["$key->department_name"] = $result->count;
        }
        return $department_array;
    }

    public function get_department_list(){
        $departments = $this->db_readonly->select("it.assigned_to_department_id as department_id, sd.department")
        ->from('internal_ticketing_items it')
        ->join('staff_departments sd', 'sd.id=it.assigned_to_department_id')
        ->group_by('it.assigned_to_department_id')
        ->get()->result();

        return $departments;
    }

    public function get_resolved_ticket_count_department_wise(){
        $department = $_POST['department'];
        $resolved_ticket_count = $this->db_readonly->select("count(it.id) as resolved_ticket_count")
        ->from('internal_ticketing_items it')
        ->where('it.assigned_to_department_id', $department)
        ->where('it.ticket_status', 'Resolved')
        ->get()->result();

        return $resolved_ticket_count;
    }

    public function get_ticket_count_department_wise(){
        $result = new stdClass();
        $CURRENT_DATE = date('Y-m-d');
        $department = $_POST['department'];
        $filter = $_POST['filter'];
        $this->db_readonly->select("count(it.id) as created_ticket_count");
        $this->db_readonly->from('internal_ticketing_items it');
        if($department != 'All'){
            $this->db_readonly->where('it.assigned_to_department_id', $department);
        }
        $this->db_readonly->where_in('it.ticket_status', ['Open', 'Reopen']);
        if ($filter == '7'){
            $this->db_readonly->where('date_format(it.created_on,"%Y-%m-%d") >= date_format(DATE_ADD("'.$CURRENT_DATE.'", INTERVAL -7 DAY),"%Y-%m-%d")');
        }
        elseif ($filter == '30'){
            $this->db_readonly->where('date_format(it.created_on,"%Y-%m-%d") >= date_format(DATE_ADD("'.$CURRENT_DATE.'", INTERVAL -30 DAY),"%Y-%m-%d")');
        }
        $created_ticket_count = $this->db_readonly->get()->row();

        $this->db_readonly->select("count(it.id) as in_progress_ticket_count");
        $this->db_readonly->from('internal_ticketing_items it');
        if($department != 'All'){
            $this->db_readonly->where('it.assigned_to_department_id', $department);
        }
        $this->db_readonly->where('it.ticket_status', 'In Progress');
        if ($filter == '7'){
            $this->db_readonly->where('date_format(it.in_progress_marked_time,"%Y-%m-%d") >= date_format(DATE_ADD("'.$CURRENT_DATE.'", INTERVAL -7 DAY),"%Y-%m-%d")');
        }
        elseif ($filter == '30'){
            $this->db_readonly->where('date_format(it.in_progress_marked_time,"%Y-%m-%d") >= date_format(DATE_ADD("'.$CURRENT_DATE.'", INTERVAL -30 DAY),"%Y-%m-%d")');
        }
        $in_progress_ticket_count = $this->db_readonly->get()->row();
        

        $this->db_readonly->select("count(it.id) as resolved_ticket_count");
        $this->db_readonly->from('internal_ticketing_items it');
        if($department != 'All'){
            $this->db_readonly->where('it.assigned_to_department_id', $department);
        }
        $this->db_readonly->where('it.ticket_status', 'Resolved');
        if ($filter == '7'){
            $this->db_readonly->where('date_format(it.resolved_on,"%Y-%m-%d") >= date_format(DATE_ADD("'.$CURRENT_DATE.'", INTERVAL -7 DAY),"%Y-%m-%d")');
        }
        elseif ($filter == '30'){
            $this->db_readonly->where('date_format(it.resolved_on,"%Y-%m-%d") >= date_format(DATE_ADD("'.$CURRENT_DATE.'", INTERVAL -30 DAY),"%Y-%m-%d")');
        }
        $resolved_ticket_count = $this->db_readonly->get()->row();

        $this->db_readonly->select("count(it.id) as closed_ticket_count");
        $this->db_readonly->from('internal_ticketing_items it');
        if($department != 'All'){
            $this->db_readonly->where('it.assigned_to_department_id', $department);
        }
        $this->db_readonly->where('it.ticket_status', 'Closed');
        if ($filter == '7'){
            $this->db_readonly->where('date_format(it.closed_on,"%Y-%m-%d") >= date_format(DATE_ADD("'.$CURRENT_DATE.'", INTERVAL -7 DAY),"%Y-%m-%d")');
        }
        elseif ($filter == '30'){
            $this->db_readonly->where('date_format(it.closed_on,"%Y-%m-%d") >= date_format(DATE_ADD("'.$CURRENT_DATE.'", INTERVAL -30 DAY),"%Y-%m-%d")');
        }
        $closed_ticket_count = $this->db_readonly->get()->row();

        $result->closed_ticket_count = $closed_ticket_count->closed_ticket_count;
        $result->created_ticket_count = $created_ticket_count->created_ticket_count;
        $result->resolved_ticket_count = $resolved_ticket_count->resolved_ticket_count;
        $result->in_progress_ticket_count = $in_progress_ticket_count->in_progress_ticket_count;
        return $result;
    }

    public function get_department_wise_priority_data(){
        $CURRENT_DATE = date('Y-m-d');
        $department = $_POST['department'];
        $priority_array = [];
        $filter = $_POST['filter'];
        $priorities = $this->db_readonly->select("ip.priority_name, ip.id as priority_id")
        ->from('internal_ticketing_priority ip')
        ->where('ip.status', 1)
        ->get()->result();

        foreach ($priorities as $key) {
            $this->db_readonly->select("count(it.id) as count");
            $this->db_readonly->from('internal_ticketing_items it');
            $this->db_readonly->where('it.priority', $key->priority_id);
            if($department != 'All'){
                $this->db_readonly->where('it.assigned_to_department_id', $department);
            }
            if ($filter == '7'){
                $this->db_readonly->where('date_format(it.created_on,"%Y-%m-%d") >= date_format(DATE_ADD("'.$CURRENT_DATE.'", INTERVAL -7 DAY),"%Y-%m-%d")');
            }
            elseif ($filter == '30'){
                $this->db_readonly->where('date_format(it.created_on,"%Y-%m-%d") >= date_format(DATE_ADD("'.$CURRENT_DATE.'", INTERVAL -30 DAY),"%Y-%m-%d")');
            }
            $result = $this->db_readonly->get()->row();
            $priority_array["$key->priority_name"] = $result->count;
        }
        return $priority_array;
    }

    public function get_closure_rating_data(){
        $CURRENT_DATE = date('Y-m-d');
        $department = $_POST['department'];
        $rating_array = [];
        $filter = $_POST['filter'];
        $star_rating = $this->db_readonly->select("it.closure_rating")
        ->from('internal_ticketing_items it')
        ->group_by('it.closure_rating')
        ->get()->result();

        foreach ($star_rating as $key) {
            $this->db_readonly->select("count(it.id) as count");
            $this->db_readonly->from('internal_ticketing_items it');
            $this->db_readonly->where('it.closure_rating', $key->closure_rating);
            if($department != 'All'){
                $this->db_readonly->where('it.assigned_to_department_id', $department);
            }
            if ($filter == '7'){
                $this->db_readonly->where('date_format(it.closed_on,"%Y-%m-%d") >= date_format(DATE_ADD("'.$CURRENT_DATE.'", INTERVAL -7 DAY),"%Y-%m-%d")');
            }
            elseif ($filter == '30'){
                $this->db_readonly->where('date_format(it.closed_on,"%Y-%m-%d") >= date_format(DATE_ADD("'.$CURRENT_DATE.'", INTERVAL -30 DAY),"%Y-%m-%d")');
            }
            $result = $this->db_readonly->get()->row();
            $rating_array["$key->closure_rating"] = $result->count;
        }
        return $rating_array;
    }

    
  public function get_internal_ticket_summary($from_date, $to_date, $department){
    $this->db_readonly->select("count(itt.id) as internal_tickets,date_format(itt.created_on,'%d-%b') as created_on");
    $this->db_readonly->from('internal_ticketing_items itt');
    $this->db_readonly->where_in('itt.ticket_status',['Open', 'In Progress', 'Reopen']);
    $this->db_readonly->group_by("date_format(itt.created_on,'%d-%m-%Y')");
    $this->db_readonly->order_by('itt.created_on','desc');
    if($department != 'All'){
        $this->db_readonly->where('itt.assigned_to_department_id', $department);
    }
    $this->db_readonly->where('date_format(itt.created_on,"%Y-%m-%d") BETWEEN "'.$to_date. '" and "'.$from_date.'"');
    

    $dateArry = array();         
    $toDate = strtotime($to_date); 
    $fromDate = strtotime($from_date);
    for ($currentDate = $toDate; $currentDate <= $fromDate; $currentDate += (86400)) {                         
      $Store = date('d-M', $currentDate); 
      $dateArry[$Store] = array('internal_tickets'=>"0",'created_on'=>$Store,); 
    }
    $transCount = $this->db_readonly->get()->result();
    foreach ($transCount as $key => $val) {
      if (array_key_exists($val->created_on, $dateArry)) {
        $dateArry[$val->created_on] = $val;
      }
    }
    
    // $open_tickets = $this->db->select("count(*) as open_tickets")
    // ->from('internal_ticketing_items')
    // ->where_in('ticket_status', ['Open','In Progress','Reopen'])
    // ->get()->row();

    // $resolved_tickets = $this->db->select("count(*) as resolved_tickets")
    // ->from('internal_ticketing_items')
    // ->where('ticket_status', 'Resolved')
    // ->where("date(created_on) = curdate()")
    // ->get()->row();

    // $counts = new stdClass();
    // $counts->open_tickets = $open_tickets->open_tickets;
    // $counts->closed_tickets = $resolved_tickets->resolved_tickets;
    // $counts->dateArry = $dateArry;
    return $dateArry;

  }

  public function get_weekly_internal_ticket_summary($department){
    $from_date = $this->db_readonly->select('date_format(it.created_on,"%Y-%m-%d") as from_date')
    ->from('internal_ticketing_items it')
    ->order_by('it.created_on')
    ->limit('1')
    ->get()->row();
    // echo "<pre>"; print_r($from_date->from_date);

    $to_date = $this->db_readonly->select('date_format(it.created_on,"%Y-%m-%d") as to_date')
    ->from('internal_ticketing_items it')
    ->order_by('it.created_on', 'DESC')
    ->limit('1')
    ->get()->row();
    // echo "<pre>"; print_r($to_date->to_date);

    $data = $this->db_readonly->select("count(it.id) as weekly_ticket_count, WEEK(it.created_on) as week_number") 
    ->from('internal_ticketing_items it')
    ->where('date_format(it.created_on,"%Y-%m-%d") BETWEEN "'.$from_date->from_date.'" AND "'.$to_date->to_date.'"')
    ->group_by('WEEK(it.created_on)')
    ->get()->result();
    $week_array = [];
    foreach($data as $val){
        $counts = new stdClass();
        $counts->weekly_ticket_count = $val->weekly_ticket_count;
        $week_array['Week '.$val->week_number] = $counts;
        // echo "<pre>"; print_r($val->weekly_ticket_count); 
    }
    return $week_array;
    }


  public function get_ticket_trend_count($fromDate, $toDate, $department){
    $this->db_readonly->select("count(*) as open_tickets");
    $this->db_readonly->from('internal_ticketing_items');
    $this->db_readonly->where_in('ticket_status', ['Open','In Progress','Reopen']);
    if($department != 'All'){
        $this->db_readonly->where('assigned_to_department_id', $department);
    }
    $this->db_readonly->where('date_format(created_on,"%Y-%m-%d") BETWEEN "'.$toDate. '" and "'.$fromDate.'"');

    $open_tickets = $this->db_readonly->get()->row();

    $this->db_readonly->select("count(*) as resolved_tickets");
    $this->db_readonly->from('internal_ticketing_items');
    $this->db_readonly->where('ticket_status', 'Resolved');
    if($department != 'All'){
        $this->db_readonly->where('assigned_to_department_id', $department);
        
    }
    $this->db_readonly->where('date_format(resolved_on,"%Y-%m-%d") BETWEEN "'.$toDate. '" and "'.$fromDate.'"');

    $resolved_tickets = $this->db_readonly->get()->row();
    $counts = new stdClass();
    $counts->open_tickets = $open_tickets->open_tickets;
    $counts->resolved_tickets = $resolved_tickets->resolved_tickets;
    return $counts;
  }

    public function get_open_ticket_count(){
        $CURRENT_DATE = date('Y-m-d');
        $filter = $_POST['filter'];
        $this->db_readonly->select('count(it.id) as open_ticket_count');
        $this->db_readonly->from("internal_ticketing_items it");
        $this->db_readonly->where_in('it.ticket_status', ['Open','In Progress','Reopen']);
        if ($filter == '7'){
            $this->db_readonly->where('date_format(it.created_on,"%Y-%m-%d") >= date_format(DATE_ADD("'.$CURRENT_DATE.'", INTERVAL -7 DAY),"%Y-%m-%d")');
        }
        elseif ($filter == '30'){
            $this->db_readonly->where('date_format(it.created_on,"%Y-%m-%d") >= date_format(DATE_ADD("'.$CURRENT_DATE.'", INTERVAL -30 DAY),"%Y-%m-%d")');
        }
        $result = $this->db_readonly->get()->row();
        return $result;
    }

    public function get_weekly_report(){
        $from_date = $_POST['from_date'];
        $to_date = $_POST['to_date'];
        $staff_id = $_POST['staff_id'];
        $issue_type_sql_query="";
        if(! empty ($_POST['issue_type'])){
            $issue_type_sql_query="and issue_type =".$_POST['issue_type'];
        }

        $sql = "select DATE_FORMAT(created_on, '%d-%m-%Y') as date, count(*) as count
                from internal_ticketing_items
                where date(created_on) >= STR_TO_DATE('$from_date', '%d-%m-%Y') and date(created_on) <= STR_TO_DATE('$to_date', '%d-%m-%Y') and assigned_to = $staff_id $issue_type_sql_query 
                group by date(created_on), assigned_to";
        $res = $this->db_readonly->query($sql)->result();
        return $res;
    }

    public function get_report(){
        $issue_type_sql_query="";
        if(! empty ($_POST['issue_type'])){
            $issue_type_sql_query="and issue_type =".$_POST['issue_type'];
        }
        $from_date = $_POST['from_date'];
        $to_date = $_POST['to_date'];
        if(! empty($_POST['staff_id'])){
            $staff_id=implode(',', $_POST['staff_id']);
        }
        
        $sql = "select it.*, sm.first_name,sd.department,itp.priority_name,itit.issue_name,itrt.resolution_name
                from internal_ticketing_items it
                join staff_master sm on sm.id = it.assigned_to
                join staff_departments sd on it.assigned_to_department_id=sd.id
                left join internal_ticketing_priority itp on itp.id=it.priority
                join internal_ticketing_issue_type itit on itit.id=it.issue_type
                left join internal_ticketing_resolution_type itrt on itrt.id=it.nature_of_resolution
                where date(it.created_on) >= STR_TO_DATE('$from_date', '%d-%m-%Y') and date(it.created_on) <= STR_TO_DATE('$to_date', '%d-%m-%Y') $issue_type_sql_query";
        if(! empty($staff_id)){
            $sql.= " and sm.id in($staff_id) ";
        }
        // $sql.= "group by date(it.created_on), assigned_to;";
        $res = $this->db_readonly->query($sql)->result();
        foreach ($res as $key => $value) {
            $value->assigned_to = $this->_getAvatarNameById($value->assigned_to);
            $value->resolved_by = $this->_getAvatarNameById($value->resolved_by);
            $value->closed_by = $this->_getAvatarNameById($value->closed_by);
            $value->in_progress_by = $this->_getAvatarNameById($value->in_progress_by);
            $value->reported_by = $this->_getAvatarNameById($value->reported_by);
            $value->created_by = $this->_getAvatarNameById($value->created_by);
            $value->created_on = local_time($value->created_on, 'd-M-Y h:i a');
            $value->closed_on = local_time($value->closed_on, 'd-M-Y h:i a');
            $value->due_date = local_time($value->due_date, 'd-M-Y h:i a');
            $value->in_progress_marked_time = local_time($value->in_progress_marked_time, 'd-M-Y h:i a');
            $value->resolved_on = local_time($value->resolved_on, 'd-M-Y h:i a');
        }
        return $res;
    }
    
    public function get_report_by_date(){
        $date = $_POST['date'];
        $staff_id = $_POST['staff_id'];
        $sql = "SELECT title, description, issue_type, issue_name, ticket_status, DATE_FORMAT(created_on, '%d-%m-%Y') AS created_date, DATE_FORMAT(created_on, '%h:%i %p') AS created_time, DATE_FORMAT(resolved_on, '%d-%m-%Y') AS resolved_date, DATE_FORMAT(resolved_on, '%h:%i %p') AS resolved_time, priority, TIMESTAMPDIFF(HOUR, created_on, resolved_on) AS diff_hours, MOD(TIMESTAMPDIFF(MINUTE, created_on, resolved_on), 60) AS diff_minutes
                FROM internal_ticketing_items i
                JOIN internal_ticketing_issue_type it ON i.issue_type = it.id
                WHERE assigned_to = $staff_id AND DATE(created_on) = STR_TO_DATE('$date', '%d-%m-%Y')";
        $res = $this->db_readonly->query($sql)->result();
        return $res;
    }

    public function get_resolved_ticket_count(){
        $CURRENT_DATE = date('Y-m-d');
        $filter = $_POST['filter'];
        $this->db_readonly->select('count(it.id) as resolved_ticket_count');
        $this->db_readonly->from("internal_ticketing_items it");
        $this->db_readonly->where_in('it.ticket_status', 'Resolved');
        if ($filter == '7'){
            $this->db_readonly->where('date_format(it.resolved_on,"%Y-%m-%d") >= date_format(DATE_ADD("'.$CURRENT_DATE.'", INTERVAL -7 DAY),"%Y-%m-%d")');
        }
        elseif ($filter == '30'){
            $this->db_readonly->where('date_format(it.resolved_on,"%Y-%m-%d") >= date_format(DATE_ADD("'.$CURRENT_DATE.'", INTERVAL -30 DAY),"%Y-%m-%d")');
        }
        $result = $this->db_readonly->get()->row();
        return $result;
    }

    public function get_top_reporter(){
        $CURRENT_DATE = date('Y-m-d');
        $filter = $_POST['filter'];
        $this->db_readonly->select("it.reported_by, count(it.reported_by) as number_of_tickets, CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) AS staff_name");
        $this->db_readonly->from("internal_ticketing_items it");
        $this->db_readonly->join('staff_master sm', 'sm.id=it.reported_by');
        if ($filter == '7'){
            $this->db_readonly->where('date_format(it.created_on,"%Y-%m-%d") >= date_format(DATE_ADD("'.$CURRENT_DATE.'", INTERVAL -7 DAY),"%Y-%m-%d")');
        }
        elseif ($filter == '30'){
            $this->db_readonly->where('date_format(it.created_on,"%Y-%m-%d") >= date_format(DATE_ADD("'.$CURRENT_DATE.'", INTERVAL -30 DAY),"%Y-%m-%d")');
        }
        $this->db_readonly->group_by('it.reported_by');
        $this->db_readonly->order_by('number_of_tickets', 'DESC');
        $this->db_readonly->limit('1');
        $result = $this->db_readonly->get()->row();
        return $result;
    }

    public function get_top_resolver(){
        $CURRENT_DATE = date('Y-m-d');
        $filter = $_POST['filter'];
        $this->db_readonly->select("it.resolved_by, count(it.resolved_by) as number_of_tickets, CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) AS staff_name");
        $this->db_readonly->from("internal_ticketing_items it");
        $this->db_readonly->join('staff_master sm', 'sm.id=it.resolved_by');
        if ($filter == '7'){
            $this->db_readonly->where('date_format(it.resolved_on,"%Y-%m-%d") >= date_format(DATE_ADD("'.$CURRENT_DATE.'", INTERVAL -7 DAY),"%Y-%m-%d")');
        }
        elseif ($filter == '30'){
            $this->db_readonly->where('date_format(it.resolved_on,"%Y-%m-%d") >= date_format(DATE_ADD("'.$CURRENT_DATE.'", INTERVAL -30 DAY),"%Y-%m-%d")');
        }
        $this->db_readonly->group_by('it.resolved_by');
        $this->db_readonly->order_by('number_of_tickets', 'DESC');
        $this->db_readonly->limit('1');
        $result = $this->db_readonly->get()->row();
        return $result;
    }

    public function get_tickets_with_resolution_time($department){
        $CURRENT_DATE = date('Y-m-d');
        $filter = $_POST['filter'];
        $this->db_readonly->select('DATEDIFF(it.resolved_on, it.created_on) as resolution_time, it.id');
        $this->db_readonly->from('internal_ticketing_items it');
        $this->db_readonly->where('it.ticket_status', 'Resolved');
        if($department != 'All'){
            $this->db_readonly->where('it.assigned_to_department_id', $department);
        }
        if ($filter == '7'){
            $this->db_readonly->where('date_format(it.resolved_on,"%Y-%m-%d") >= date_format(DATE_ADD("'.$CURRENT_DATE.'", INTERVAL -7 DAY),"%Y-%m-%d")');
        }
        elseif ($filter == '30'){
            $this->db_readonly->where('date_format(it.resolved_on,"%Y-%m-%d") >= date_format(DATE_ADD("'.$CURRENT_DATE.'", INTERVAL -30 DAY),"%Y-%m-%d")');
        }
        $result = $this->db_readonly->get()->result();
        $output_array = new stdClass();
        $output_array->{'<1 day'} = 0;
        $output_array->{'1-3 days'} = 0;
        $output_array->{'>3 days'} = 0;

        foreach($result as $res){
            if(intval($res->resolution_time) < 1){
                $output_array->{'<1 day'} += 1;
            }else if(intval($res->resolution_time) >= 1 &&  intval($res->resolution_time) < 3){
                $output_array->{'1-3 days'} += 1;
            }else if(intval($res->resolution_time) >= 3){
                $output_array->{'>3 days'} += 1;
            }
        }
        return $output_array;
    }

    private function _getAvatarNameById($avatarId) {
        $collected = $this->db_readonly->select('CONCAT(ifnull(sm.first_name," "), " ", ifnull(sm.last_name," ")) as staffName')
            ->from('staff_master sm')
            ->join('avatar a', 'sm.id=a.stakeholder_id')
            ->where('a.avatar_type', '4') // 4 avatar type staff        
            ->where('sm.id',$avatarId)
            ->get()->row();
        if (!empty($collected)) {
          return $collected->staffName;
        }else{
          return 'Admin';
        }
      }
}


