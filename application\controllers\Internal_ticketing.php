<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON><PERSON><PERSON><PERSON> P
 *
 * Created:  24 October 2023
 *
 * Description: Controller for Infirmary Module. Entry point for Infirmary Module
 *
 * Requirements: PHP5 or above
 *
 */

class Internal_ticketing extends CI_Controller {
	function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('INTERNAL_TICKETING') || !$this->authorization->isAuthorized('INTERNAL_TICKETING.MODULE')) {
      redirect('dashboard', 'refresh');
    }
    $this->load->library('filemanager');
    $this->load->model('Internalticketing_model');
    $this->load->model('email_model');
    $this->load->helper('email_helper');
    $this->load->helper('texting_helper');
  }

  //Landing function to show Infirmary Menu
  public function index() {
    $site_url = site_url();
    $data['master_tiles'] = array(
      [
        'title' => 'Create Ticket',
        'sub_title' => 'Create Ticket',
        'icon' => 'svg_icons/subjects.svg',
        'url' => $site_url.'internal_ticketing/create_ticket',
        'permission' => $this->authorization->isAuthorized('INTERNAL_TICKETING.INTERNAL_TICKETING_ADMIN')
      ],
      [
        'title' => 'Manage Tickets',
        'sub_title' => 'Manage Tickets',
        'icon' => 'svg_icons/subjects.svg',
        'url' => $site_url.'internal_ticketing/manage_tickets',
        'permission' => $this->authorization->isAuthorized('INTERNAL_TICKETING.INTERNAL_TICKETING_ADMIN')
      ]
    );
    
    $data['admin_tiles'] = array(
      [
        'title' => 'Manage Priority',
        'sub_title' => 'Manage Priority',
        'icon' => 'svg_icons/subjects.svg',
        'url' => $site_url.'internal_ticketing/manage_priority',
        'permission' => $this->authorization->isAuthorized('INTERNAL_TICKETING.INTERNAL_TICKETING_ADMIN')
      ],
      [
        'title' => 'Manage Issue Type',
        'sub_title' => 'Manage Issue Type',
        'icon' => 'svg_icons/subjects.svg',
        'url' => $site_url.'internal_ticketing/manage_issue_type',
        'permission' => $this->authorization->isAuthorized('INTERNAL_TICKETING.INTERNAL_TICKETING_ADMIN')
      ],
      [
        'title' => 'Manage Resolution Type',
        'sub_title' => 'Manage Resolution Type',
        'icon' => 'svg_icons/subjects.svg',
        'url' => $site_url.'internal_ticketing/manage_resolution_type',
        'permission' => $this->authorization->isAuthorized('INTERNAL_TICKETING.INTERNAL_TICKETING_ADMIN')
      ],
      // [
      //   'title' => 'Ticket Analysis',
      //   'sub_title' => 'Ticket Analysis',
      //   'icon' => 'svg_icons/subjects.svg',
      //   'url' => $site_url.'internal_ticketing/ticket_analysis',
      //   'permission' => $this->authorization->isSuperAdmin()
      // ]
    );
    $data['report_tiles'] = array(
        // [
        //   'title' => 'Department/Priority Report',
        //   'sub_title' => 'Departmet/Priority wise reports',
        //   'icon' => 'svg_icons/subjects.svg',
        //   'url' => $site_url.'internal_ticketing/department_priority_reports',
        //   'permission' => $this->authorization->isAuthorized('INTERNAL_TICKETING.INTERNAL_TICKETING_ADMIN')
        // ],
        [
          'title' => 'Ticket Analysis',
          'sub_title' => 'Ticket Analysis',
          'icon' => 'svg_icons/subjects.svg',
          'url' => $site_url.'internal_ticketing/ticket_analysis',
          'permission' => $this->authorization->isSuperAdmin()
        ],
        [
          'title' => 'Weekly Report',
          'sub_title' => 'Weekly Report',
          'icon' => 'svg_icons/subjects.svg',
          'url' => $site_url.'internal_ticketing/weekly_report',
          'permission' => $this->authorization->isSuperAdmin()
        ],
        
      );
    
    $data['admin_tiles'] = checkTilePermissions($data['admin_tiles']);
    $data['resolved_tickets_created_by_me'] = $this->Internalticketing_model->get_resolved_ticket_count_details_created_by_me();
    $data['assigned_to_my_department'] = $this->Internalticketing_model->get_assigned_to_my_dept_ticket_count_details();
    $data['assigned_to_me'] = $this->Internalticketing_model->tickets_assigned_to_me();
    $data['open_tickets_created_by_me'] = $this->Internalticketing_model->get_open_ticket_count_details_created_by_me();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content']    = 'internal_ticketing/mobile_menu';
    } else if ($this->mobile_detect->isMobile()) {
      $data['main_content']    = 'internal_ticketing/mobile_menu';
    }else{
      $data['main_content']    = 'internal_ticketing/menu';

    }
    $this->load->view('inc/template', $data);
  }

  public function get_all_tickets_data(){
    $result = $this->Internalticketing_model->get_all_tickets_data();
    echo json_encode($result);
  }

  public function get_department_wise_tickets_priority_data(){
    $result = $this->Internalticketing_model->get_department_wise_tickets_priority_data();
    echo json_encode($result);
  }
  
//   ************* Manage Priority Start *****************
  public function manage_priority(){
    $data['priority_details'] = $this->Internalticketing_model->get_priority_types();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content']='internal_ticketing/manage_priority_tablet';
    } else if ($this->mobile_detect->isMobile()) {
      $data['main_content']='internal_ticketing/manage_priority_mobile';
    } else {
      $data['main_content']='internal_ticketing/manage_priority';
    }
    $this->load->view('inc/template',$data);
  }

  public function add_priority(){
    echo $this->Internalticketing_model->add_priority();
  }

  public function get_priority_types(){
    $result = $this->Internalticketing_model->get_priority_types();
    echo json_encode($result);
  }

  public function activate_deactiviate_priority(){
    $priority_id = $_POST['id'];
    echo $this->Internalticketing_model->update_priority_status($priority_id);
  }

  public function get_priority_desc(){
    $priority_id = $_POST['priority'];
    echo json_encode($this->Internalticketing_model->get_priority_desc($priority_id));
  }

  public function get_priority_details(){
    $ticket_id = $_POST['ticket_id'];
    echo json_encode($this->Internalticketing_model->get_priority_details($ticket_id));
  }

  public function edit_priority(){
    $priority_id = $_POST['priority_id'];
    echo $this->Internalticketing_model->edit_priority($priority_id);
  }

//   ************* Manage Priority End *****************

//   ************* Manage Resolution Start *****************
public function manage_resolution_type(){
  $data['resolution_type_details'] = $this->Internalticketing_model->get_resolution_types();
  if ($this->mobile_detect->isTablet()) {
    $data['main_content']='internal_ticketing/manage_resolution_types_tablet';
  } else if ($this->mobile_detect->isMobile()) {
    $data['main_content']='internal_ticketing/manage_resolution_types_mobile';
  } else {
    $data['main_content']='internal_ticketing/manage_resolution_types';
  }
  $this->load->view('inc/template',$data);
}

public function add_resolution_type(){
  $resolution_name = $_POST['resolution_name'];
  echo $this->Internalticketing_model->add_resolution_type($resolution_name);
}

public function get_resolution_types(){
  $result = $this->Internalticketing_model->get_resolution_types();
  echo json_encode($result);
}

public function activate_deactiviate_resolution_type(){
  $resolution_type_id = $_POST['id'];
  echo $this->Internalticketing_model->update_resolution_type_status($resolution_type_id);
}

//   ************* Manage Resolution End *****************

//   ************* Manage Issue Type Start *****************


public function manage_issue_type(){
    $data['issue_type_details'] = $this->Internalticketing_model->get_issue_types();
    // print_r($data['issue_type_details']);die();
    $data['department_list'] = $this->Internalticketing_model->get_departments();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content']='internal_ticketing/manage_issue_type_tablet';
    } else if ($this->mobile_detect->isMobile()) {
      $data['main_content']='internal_ticketing/manage_issue_type_mobile';
    } else {
      $data['main_content']='internal_ticketing/manage_issue_type';
    }
    $this->load->view('inc/template',$data);
  }

  public function add_issue_type(){
    $issue_name = $_POST['issue_name'];
    $default_department_id = $_POST['default_department_id'];
    $first_level_assignee = $_POST['first_level_assignee'];
    $second_level_assignee = $_POST['second_level_assignee'];
    $third_level_assignee = $_POST['third_level_assignee'];

    echo $this->Internalticketing_model->add_issue_type($issue_name, $default_department_id, $first_level_assignee, $second_level_assignee, $third_level_assignee);
  }

  public function get_issue_types(){
    $result = $this->Internalticketing_model->get_issue_types();
    echo json_encode($result);
  }

  public function get_staff(){
    $department_id = $_POST['department_id'];
    echo json_encode($this->Internalticketing_model->get_staff($department_id));
  }
  
  public function update_issue_type_status(){
    $issue_id = $_POST['id'];
    echo $this->Internalticketing_model->update_issue_type_status($issue_id);
  }

  //   ************* Manage Issue Type End *****************

  //   ************* Add Tickets *****************
  public function create_ticket(){
    $data['issue_type'] = $this->Internalticketing_model->get_issues();
    $data['priority'] = $this->Internalticketing_model->get_priorities();
    $data['staff_list'] = $this->Internalticketing_model->get_all_staff();
    $data['logged_in_staff'] = $this->authorization->getAvatarStakeHolderId();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'internal_ticketing/create_ticket_tablet';
      $this->load->view('inc/template', $data);
    } else if ($this->mobile_detect->isMobile()) {
      $data['main_content'] = 'internal_ticketing/create_ticket_mobile';
      $this->load->view('inc/template', $data);
    } else {
      $data['main_content'] = 'internal_ticketing/create_ticket';
      $this->load->view('inc_v2/template', $data);
    }
  }

  public function get_assigned_staff(){
    $issue_type = $_POST['issue_type'];
    echo json_encode($this->Internalticketing_model->get_assigned_staff($issue_type));
  }

  public function submit_ticket_data(){
    $ticket_number = $this->submit_ticket();   
  }

  public function submit_ticket(){
    $title = $_POST['title'];
    $issue_type = $_POST['issue_type'];
    $priority = $_POST['priority'];
    $description = $_POST['description'];
		$paths = isset($_POST['paths'])? $_POST['paths'] : '';
    $created_by = $_POST['created_by'];
    $reported_by_name = $_POST['reported_by_name'];
    if (isset($_POST['reported_by'])) {
      $reported_by = empty($_POST['reported_by']) ? $_POST['created_by'] : $_POST['reported_by'];
    } else {
      $reported_by = $_POST['created_by'];
    }
    if (isset($_POST['confidentiality'])) {
      $confidentiality = $_POST['confidentiality'];
    }else{
      $confidentiality = '0';
    }

    $ticket_item_id = $this->Internalticketing_model->insert_ticket_data($title, $paths, $issue_type, $priority, $description, $created_by, $reported_by, $reported_by_name, $confidentiality);
    if ($ticket_item_id){
      $assigned_staff_obj = $this->Internalticketing_model->get_assigned_internal_ticket_staff_id($ticket_item_id);
      $staff_ids = $this->Internalticketing_model->get_assigned_staff_id($ticket_item_id);
      if(!empty($staff_ids)) {
        $notify_array = array();
        $notify_array['staff_ids'] = $staff_ids;
        $notify_array['title'] = 'Ticket Assigned';
        $notify_array['message'] = "Ticket $assigned_staff_obj->ticket_item_id with title '$assigned_staff_obj->title' reported by " . trim($reported_by_name) . " is assigned to you";
        $notify_array['mode'] = 'notification';
        $notify_array['source'] = 'Internal Ticketing';
        sendText($notify_array);
      } 
      $sender_list = [];
      $emaildata = array();
      if(!empty($assigned_staff_obj)){
        $obj = new stdClass();
        $sender_list['staff'] = [
          'send_to_type' => 'Staff',
          'id' => $assigned_staff_obj->staff_id,
        ];
        $obj->id = $assigned_staff_obj->staff_id;
        $obj->email = $assigned_staff_obj->email;
        $obj->avatar_type = '4';
        array_push($emaildata, $obj);
      }
      $email_body = 
      "Hi, Ticket $assigned_staff_obj->ticket_item_id with title '$assigned_staff_obj->title' reported by " . trim($reported_by_name) . " is assigned to you. <br><br>
      Ticket details is as follows: <br><br>
      <table>
        <tr>
          <td>Ticket number</td>
          <td>$assigned_staff_obj->ticket_item_id</td>
        </tr>
        <tr>
          <td>Title</td>
          <td>$assigned_staff_obj->title</td>
        </tr>
        <tr>
          <td>Description</td>
          <td>$assigned_staff_obj->description</td>
        </tr>
        <tr>
          <td>Priority</td>
          <td>$assigned_staff_obj->priority_name</td>
        </tr>
        <tr>
          <td>Escalation Status</td>
          <td>$assigned_staff_obj->escalation_status</td>
        </tr>
        <tr>
          <td>Issue Type</td>
          <td>$assigned_staff_obj->issue_name</td>
        </tr>
        <tr>
          <td>Assigned Department</td>
          <td>$assigned_staff_obj->department_name</td>
        </tr>
      </table>
      <br>
      Thank you";
      $from_email = $this->settings->getSetting('internal_ticketing_from_email');
      $email_master_data = array(
        'subject' => 'Ticket Assigned',
        'body' => $email_body,
        'source' => 'Internal Ticketing',
        'sent_by' => $this->authorization->getAvatarId(),
        'recievers' => 'Staff',
        'from_email' => $from_email,
        'files' => '',
        'acad_year_id' => $this->acad_year->getAcadYearId(),
        'visible' => 1,
        'sender_list' => empty($sender_list)?NULL:json_encode($sender_list),
        'sending_status' => 'Send Email'
      );
      $this->load->model('communication/emails_model');
      $email_master_id = $this->emails_model->saveEmail($email_master_data);
      $sent_data = (object) $emaildata;
      $status = $this->emails_model->save_sending_data($sent_data, $email_master_id);
     
      $email = $this->emails_model->getEmailInfo($email_master_id);

      $email_ids = [];
      if($assigned_staff_obj->email != '' && $assigned_staff_obj->email != null) {
        array_push($email_ids, $assigned_staff_obj->email);
      }
      sendEmail($email->body, $email->subject, $email_master_id, $email_ids, $email->from_email, json_decode($email->files));
    }
    return $ticket_item_id;
  }
 
  //   ************* Add Tickets End*****************

  //   ************* Manage Tickets *****************
  public function manage_tickets(){
    $data['issue_type'] = $this->Internalticketing_model->get_issues();
    $data['priority'] = $this->Internalticketing_model->get_priorities();
    $data['department'] = $this->Internalticketing_model->get_departments_in_issue_type_table();
    $data['resolution_types'] = $this->Internalticketing_model->get_active_resolution_types();
    $data['staff_list'] = $this->Internalticketing_model->get_all_staff();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'internal_ticketing/manage_tickets_tablet';
    } else if ($this->mobile_detect->isMobile()) {
      $data['main_content'] = 'internal_ticketing/manage_tickets_mobile';
    } else {
      $data['main_content'] = 'internal_ticketing/manage_tickets';
    }
    $this->load->view('inc/template', $data);
  }

  public function my_tickets(){
    $data['issue_type'] = $this->Internalticketing_model->get_issues();
    $data['priority'] = $this->Internalticketing_model->get_priorities();
    $data['department'] = $this->Internalticketing_model->get_departments_in_issue_type_table();
    $data['resolution_types'] = $this->Internalticketing_model->get_active_resolution_types();
    $data['staff_list'] = $this->Internalticketing_model->get_all_staff();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'internal_ticketing/my_tickets_tablet';
    } else if ($this->mobile_detect->isMobile()) {
      $data['main_content'] = 'internal_ticketing/my_tickets_mobile';
    } else{
      $data['main_content'] = 'internal_ticketing/my_tickets_mobile';
    }
    $this->load->view('inc/template', $data);
  }

  public function display_ticket_details_by_id_mobile($ticket_id){
    $data['ticket_data'] = $this->Internalticketing_model->ticket_details_by_id($ticket_id);
    $data['staff_data'] = $this->Internalticketing_model->unassigned_staffs($ticket_id);
    $data['main_content'] = 'internal_ticketing/display_ticket_details_mobile';
    $this->load->view('inc/template', $data);
  }

  public function view_ticket_mobile($ticket_id){
    $data['ticket_data'] = $this->Internalticketing_model->ticket_details_by_id($ticket_id);
    $data['ticket_comments'] = $this->Internalticketing_model->get_comments($ticket_id);
    $data['resolution_types'] = $this->Internalticketing_model->get_active_resolution_types();
    $data['staff_data'] = $this->Internalticketing_model->unassigned_staffs($ticket_id);
    $data['main_content'] = 'internal_ticketing/view_ticket_mobile';
    $this->load->view('inc/template', $data);
  }

  public function add_comment(){
    $ticket_id = $this->input->post('ticket_id');
    $comment = $this->input->post('comment');
    $file_paths = $this->input->post('file_paths');
    $commented_by = $this->authorization->getAvatarStakeHolderId();

    if(empty($ticket_id) || empty($comment)) {
      echo json_encode(['status' => 'error', 'message' => 'Missing required fields']);
      return;
    }

    // Use the existing insert_comment method format
    $_POST['comments'] = $comment;
    $_POST['ticket_id'] = $ticket_id;
    $_POST['commented_by'] = $commented_by;

    // Handle file attachments
    if(!empty($file_paths) && is_array($file_paths)) {
      $_POST['file_path'] = $file_paths;
    } else {
      $_POST['file_path'] = 'null';
    }

    try {
      $result = $this->Internalticketing_model->insert_comment();

      if($result){
        echo json_encode(['status' => 'success', 'message' => 'Comment added successfully']);
      } else {
        echo json_encode(['status' => 'error', 'message' => 'Failed to add comment']);
      }
    } catch (Exception $e) {
      echo json_encode(['status' => 'error', 'message' => 'Database error: ' . $e->getMessage()]);
    }
  }

  public function update_ticket_status(){
    $ticket_id = $this->input->post('ticket_id');
    $status = $this->input->post('status');
    $remarks = $this->input->post('remarks');
    $rating = $this->input->post('rating');
    $updated_by = $this->authorization->getAvatarStakeHolderId();

    if(empty($ticket_id) || empty($status) || empty($remarks)) {
      echo json_encode(['status' => 'error', 'message' => 'Missing required fields']);
      return;
    }

    $_POST['ticket_id'] = $ticket_id;
    $_POST['closure_remarks'] = $remarks;
    $_POST['ticket_status'] = $status;
    $_POST['closed_by'] = $updated_by;
    $_POST['rating'] = $rating ?: 0;

    $result = $this->Internalticketing_model->close_reopen_ticket($ticket_id, $remarks, $status, $updated_by, $rating ?: 0);

    if($result){
      echo json_encode(['status' => 'success', 'message' => 'Status updated successfully']);
    } else {
      echo json_encode(['status' => 'error', 'message' => 'Failed to update status']);
    }
  }

  public function comments_mobile($ticket_id){
    $data['ticket_status'] = $this->Internalticketing_model->get_ticket_status($ticket_id);
    $data['ticket_comments'] = $this->Internalticketing_model->get_comments($ticket_id);
    $data['ticket_id'] = $ticket_id;
    $data['main_content'] = 'internal_ticketing/comments_mobile';
    $this->load->view('inc/template', $data);
  }

  public function resolve_mobile($ticket_id){
    $data['ticket_status'] = $this->Internalticketing_model->get_ticket_status($ticket_id);
    $data['resolution_comments'] = $this->Internalticketing_model->get_resolution_details($ticket_id);
    $data['resolution_types'] = $this->Internalticketing_model->get_active_resolution_types();
    $data['ticket_id'] = $ticket_id;
    $data['main_content'] = 'internal_ticketing/resolve_mobile';
    $this->load->view('inc/template', $data);
  }

  public function close_reopen_mobile($ticket_id){
    $data['ticket_status'] = $this->Internalticketing_model->get_ticket_status($ticket_id);
    $data['close_reopen_comments'] = $this->Internalticketing_model->get_close_reopen_comments($ticket_id);
    $data['ticket_id'] = $ticket_id;
    $data['main_content'] = 'internal_ticketing/close_reopen_mobile';
    $this->load->view('inc/template', $data);
  }

  public function history_mobile($ticket_id){
    $data['ticket_status'] = $this->Internalticketing_model->get_ticket_status($ticket_id);
    $data['history_details'] = $this->Internalticketing_model->get_history_by_ticket_id($ticket_id);
    $data['ticket_id'] = $ticket_id;
    $data['main_content'] = 'internal_ticketing/history_mobile';
    $this->load->view('inc/template', $data);
  }

  public function get_staff_data($ticket_id){
    echo json_encode($this->Internalticketing_model->get_staff_data($ticket_id));
  }

  public function add_attachments_while_create(){
    $file = $_FILES['it_docs'];
    echo $file;
  }

  public function get_unassigned_contributors(){
    $ticket_id = $_POST['ticket_id'];
    echo json_encode($this->Internalticketing_model->get_unassigned_contributors($ticket_id));
  }

  public function ticket_details_by_id(){
    $ticket_id = $_POST['ticket_id'];
    echo json_encode($this->Internalticketing_model->ticket_details_by_id($ticket_id));
  }

  public function update_ticket_progress(){
    $ticket_id = $_POST['ticket_id'];
    $due_date = $_POST['due_date'];
    $val = $this->Internalticketing_model->update_ticket_progress($ticket_id, $due_date);
    $modified_due_date = date('d-m-Y', strtotime($due_date));
    if ($val){
      $ticket_details = $this->Internalticketing_model->get_ticket_details_for_notification($ticket_id); 
      $in_progress_details = $this->Internalticketing_model->get_staff_name_and_email($ticket_id, "in_progress_by");
      $secondary_staff_emails = $this->Internalticketing_model->get_secondary_staff_emails($ticket_id);
      $staff_ids = $this->Internalticketing_model->get_ticket_creator_by_id($ticket_id);
      $secondary_staff_ids = $this->Internalticketing_model->get_secondary_staff_ids($ticket_id);

      if(!empty($staff_ids)) {
        $this->load->helper('texting_helper');
        $notify_array = array();
        $notify_array['staff_ids'] = $staff_ids;
        $notify_array['title'] = 'Ticket moved to In Progress';
        $notify_array['message'] = "Ticket $ticket_details->ticket_item_id with title '$ticket_details->title' created by you is moved to 'In Progress' by $in_progress_details->staff_name. The due date is $modified_due_date";
        $notify_array['mode'] = 'notification';
        $notify_array['source'] = 'Internal Ticketing';
        sendText($notify_array);
      }
      $sender_list = [];
      $emaildata = array();
      if(!empty($ticket_details)){
        $obj = new stdClass();
        $sender_list['staff'] = [
          'send_to_type' => 'Staff',
          'id' => $ticket_details->reported_by,
        ];
        $obj->id = $ticket_details->reported_by;
        $obj->email = $ticket_details->created_by_staff_email;
        $obj->avatar_type = '4';
        array_push($emaildata, $obj);
      }
      $email_body = 
      "Hi, Ticket $ticket_details->ticket_item_id with title '$ticket_details->title' created by you is moved to 'In Progress' by $in_progress_details->staff_name ";
      if(! empty($due_date)){
        $email_body.="and the due date is $modified_due_date.";
      }
       
      $email_body.="Ticket details is as follows: <br><br>
      <table>
        <tr>
          <td>Ticket number</td>
          <td>$ticket_details->ticket_item_id</td>
        </tr>
        <tr>
          <td>Title</td>
          <td>$ticket_details->title</td>
        </tr>
        <tr>
          <td>Description</td>
          <td>$ticket_details->description</td>
        </tr>
        <tr>
          <td>Reported By</td>
          <td>$ticket_details->created_staff_name</td>
        </tr>
        <tr>
          <td>Assigned To</td>
          <td>$ticket_details->assigned_to_staff_name</td>
        </tr>
        <tr>
          <td>Priority</td>
          <td>$ticket_details->priority_name</td>
        </tr>
        <tr>
          <td>Escalation Status</td>
          <td>$ticket_details->escalation_status</td>
        </tr>
        <tr>
          <td>Issue Type</td>
          <td>$ticket_details->issue_name</td>
        </tr>
        <tr>
          <td>Assigned Department</td>
          <td>$ticket_details->department_name</td>
        </tr>
      </table>
      <br>
      Thank you";
      $from_email = $this->settings->getSetting('internal_ticketing_from_email');
      $email_master_data = array(
        'subject' => 'Ticket Moved to In Progress',
        'body' => $email_body,
        'source' => 'Internal Ticketing',
        'sent_by' => $this->authorization->getAvatarId(),
        'recievers' => 'Staff',
        'from_email' => $from_email,
        'files' => '',
        'acad_year_id' => $this->acad_year->getAcadYearId(),
        'visible' => 1,
        'sender_list' => empty($sender_list)?NULL:json_encode($sender_list),
        'sending_status' => 'Send Email'
      );
      $this->load->model('communication/emails_model');
      $email_master_id = $this->emails_model->saveEmail($email_master_data);
      $sent_data = (object) $emaildata;
      $status = $this->emails_model->save_sending_data($sent_data, $email_master_id);
     
      $email = $this->emails_model->getEmailInfo($email_master_id);

      $email_ids = [];
      if($ticket_details->created_by_staff_email != '' && $ticket_details->created_by_staff_email != null) {
        array_push($email_ids, $ticket_details->created_by_staff_email);
      }
      sendEmail($email->body, $email->subject, $email_master_id, $email_ids, $email->from_email, json_decode($email->files));

      #notification to secondary assignees
      if(!empty($secondary_staff_ids)) {
        $this->load->helper('texting_helper');
        $notify_array = array();
        $notify_array['staff_ids'] = $secondary_staff_ids;
        $notify_array['title'] = 'Ticket Moved to In Progress';
        $notify_array['message'] = "Ticket $ticket_details->ticket_item_id with title '$ticket_details->title' is 'Resolved' by $resolved_by_staff_details->staff_name. The due date is $modified_due_date";
        $notify_array['mode'] = 'notification';
        $notify_array['source'] = 'Internal Ticketing';
        sendText($notify_array);
      }

      #email to secondary assignees
      if(!empty($secondary_staff_emails)){
        foreach($secondary_staff_emails->staff_data as $key=>$val){
          $sender_list = [];
          $emaildata = array();
          if(!empty($ticket_details)){
            $obj = new stdClass();
            $sender_list['staff'] = [
              'send_to_type' => 'Staff',
              'id' => $key,
            ];
            $obj->id = $key;
            $obj->email = $val;
            $obj->avatar_type = '4';
            array_push($emaildata, $obj);
          }
          $email_body = 
            "Hi, Ticket $ticket_details->ticket_item_id with title '$ticket_details->title' is moved to 'In Progress' by $in_progress_details->staff_name and due date is $modified_due_date. 
            Ticket details is as follows: <br><br>
            <table>
              <tr>
                <td>Ticket number</td>
                <td>$ticket_details->ticket_item_id</td>
              </tr>
              <tr>
                <td>Title</td>
                <td>$ticket_details->title</td>
              </tr>
              <tr>
                <td>Description</td>
                <td>$ticket_details->description</td>
              </tr>
              <tr>
                <td>Reported By</td>
                <td>$ticket_details->created_staff_name</td>
              </tr>
              <tr>
                <td>Assigned To</td>
                <td>$ticket_details->assigned_to_staff_name</td>
              </tr>
              <tr>
                <td>Priority</td>
                <td>$ticket_details->priority_name</td>
              </tr>
              <tr>
                <td>Escalation Status</td>
                <td>$ticket_details->escalation_status</td>
              </tr>
              <tr>
                <td>Issue Type</td>
                <td>$ticket_details->issue_name</td>
              </tr>
              <tr>
                <td>Assigned Department</td>
                <td>$ticket_details->department_name</td>
              </tr>
            </table>
            <br>
            Thank you";
            $from_email = $this->settings->getSetting('internal_ticketing_from_email');
              $email_master_data = array(
              'subject' => "Ticket Moved to In Progress",
              'body' => $email_body,
              'source' => 'Internal Ticketing',
              'sent_by' => $this->authorization->getAvatarId(),
              'recievers' => 'Staff',
              'from_email' => $from_email,
              'files' => '',
              'acad_year_id' => $this->acad_year->getAcadYearId(),
              'visible' => 1,
              'sender_list' => empty($sender_list)?NULL:json_encode($sender_list),
              'sending_status' => 'Send Email'
          );
          $this->load->model('communication/emails_model');
          $email_master_id = $this->emails_model->saveEmail($email_master_data);
          $sent_data = (object) $emaildata;
          $status = $this->emails_model->save_sending_data($sent_data, $email_master_id);
          $email = $this->emails_model->getEmailInfo($email_master_id);
          $email_ids = [];
          if($val != '' && $val != null) {
            array_push($email_ids, $val);
          }
          sendEmail($email->body, $email->subject, $email_master_id, $email_ids, $email->from_email, json_decode($email->files));
        }
      }
    }
    echo $val;
  }

  public function get_ticket_data(){
    $issue_type = $_POST['issue_type'];
    $priority = $_POST['priority'];
    $department = $_POST['department'];
    $status = $_POST['status'];
    $reported_by_me = $_POST['reported_by_me'];
    $assigned_to_me = $_POST['assigned_to_me'];
    $filter_type = $_POST['filter_type'];
    $val = $this->Internalticketing_model->get_ticket_details($issue_type, $priority, $department, $status, $reported_by_me, $assigned_to_me, $filter_type);
    echo json_encode($val);
  }

  public function update_resolution_details(){
    $ticket_id = $_POST['ticket_id'];
    $resolution_remarks = $_POST['resolution_remarks'];
    $ticket_status = $_POST['ticket_status'];
    $nature_of_resolution = $_POST['nature_of_resolution'];
    $resolved_by = $_POST['resolved_by'];
    $val = $this->Internalticketing_model->update_resolution_details($ticket_id, $resolution_remarks, $ticket_status, $nature_of_resolution, $resolved_by);
    if ($val){
      $ticket_details = $this->Internalticketing_model->get_ticket_details_for_notification($ticket_id); 
      $resolved_by_staff_details = $this->Internalticketing_model->get_staff_name_and_email($ticket_id, "resolved_by");
      $secondary_staff_emails = $this->Internalticketing_model->get_secondary_staff_emails($ticket_id);
      $staff_ids = $this->Internalticketing_model->get_ticket_creator_by_id($ticket_id);
      $secondary_staff_ids = $this->Internalticketing_model->get_secondary_staff_ids($ticket_id);
      
      if(!empty($staff_ids)) {
        $this->load->helper('texting_helper');
        $notify_array = array();
        $notify_array['staff_ids'] = $staff_ids;
        $notify_array['title'] = 'Ticket Resolved';
        $notify_array['message'] = "Ticket $ticket_details->ticket_item_id with title '$ticket_details->title' created by you is 'Resolved' by $resolved_by_staff_details->staff_name";
        $notify_array['mode'] = 'notification';
        $notify_array['source'] = 'Internal Ticketing';
        sendText($notify_array);
      }
      $sender_list = [];
      $emaildata = array();
      if(!empty($ticket_details)){
        $obj = new stdClass();
        $sender_list['staff'] = [
          'send_to_type' => 'Staff',
          'id' => $ticket_details->reported_by,
        ];
        $obj->id = $ticket_details->reported_by;
        $obj->email = $ticket_details->created_by_staff_email;
        $obj->avatar_type = '4';
        array_push($emaildata, $obj);
      }
      $email_body = 
      "Hi, Ticket $ticket_details->ticket_item_id with title '$ticket_details->title' created by you is Resolved by $resolved_by_staff_details->staff_name. 
      Ticket details is as follows: <br><br>
      <table>
        <tr>
          <td>Ticket number</td>
          <td>$ticket_details->ticket_item_id</td>
        </tr>
        <tr>
          <td>Title</td>
          <td>$ticket_details->title</td>
        </tr>
        <tr>
          <td>Description</td>
          <td>$ticket_details->description</td>
        </tr>
        <tr>
          <td>Reported By</td>
          <td>$ticket_details->created_staff_name</td>
        </tr>
        <tr>
          <td>Assigned To</td>
          <td>$ticket_details->assigned_to_staff_name</td>
        </tr>
        <tr>
          <td>Priority</td>
          <td>$ticket_details->priority_name</td>
        </tr>
        <tr>
          <td>Escalation Status</td>
          <td>$ticket_details->escalation_status</td>
        </tr>
        <tr>
          <td>Issue Type</td>
          <td>$ticket_details->issue_name</td>
        </tr>
        <tr>
          <td>Assigned Department</td>
          <td>$ticket_details->department_name</td>
        </tr>
      </table>
      <br>
      Thank you";
      $from_email = $this->settings->getSetting('internal_ticketing_from_email');
      $email_master_data = array(
        'subject' => 'Ticket Resolved',
        'body' => $email_body,
        'source' => 'Internal Ticketing',
        'sent_by' => $this->authorization->getAvatarId(),
        'recievers' => 'Staff',
        'from_email' => $from_email,
        'files' => '',
        'acad_year_id' => $this->acad_year->getAcadYearId(),
        'visible' => 1,
        'sender_list' => empty($sender_list)?NULL:json_encode($sender_list),
        'sending_status' => 'Send Email'
      );
      $this->load->model('communication/emails_model');
      $email_master_id = $this->emails_model->saveEmail($email_master_data);
      $sent_data = (object) $emaildata;
      $status = $this->emails_model->save_sending_data($sent_data, $email_master_id);
     
      $email = $this->emails_model->getEmailInfo($email_master_id);

      $email_ids = [];
      if($ticket_details->created_by_staff_email != '' && $ticket_details->created_by_staff_email != null) {
        array_push($email_ids, $ticket_details->created_by_staff_email);
      }
      sendEmail($email->body, $email->subject, $email_master_id, $email_ids, $email->from_email, json_decode($email->files));

      #notification to secondary assignees
      if(!empty($secondary_staff_ids)) {
        $this->load->helper('texting_helper');
        $notify_array = array();
        $notify_array['staff_ids'] = $secondary_staff_ids;
        $notify_array['title'] = 'Ticket Resolved';
        $notify_array['message'] = "Ticket $ticket_details->ticket_item_id with title '$ticket_details->title' is 'Resolved' by $resolved_by_staff_details->staff_name";
        $notify_array['mode'] = 'notification';
        $notify_array['source'] = 'Internal Ticketing';
        sendText($notify_array);
      }

      #email to secondary assignees
      if(!empty($secondary_staff_emails)){
        foreach($secondary_staff_emails->staff_data as $key=>$val){
          $sender_list = [];
          $emaildata = array();
          if(!empty($ticket_details)){
            $obj = new stdClass();
            $sender_list['staff'] = [
              'send_to_type' => 'Staff',
              'id' => $key,
            ];
            $obj->id = $key;
            $obj->email = $val;
            $obj->avatar_type = '4';
            array_push($emaildata, $obj);
          }
          $email_body = 
            "Hi, Ticket $ticket_details->ticket_item_id with title '$ticket_details->title' is Resolved by $resolved_by_staff_details->staff_name.
            Ticket details is as follows: <br><br>
            <table>
              <tr>
                <td>Ticket number</td>
                <td>$ticket_details->ticket_item_id</td>
              </tr>
              <tr>
                <td>Title</td>
                <td>$ticket_details->title</td>
              </tr>
              <tr>
                <td>Description</td>
                <td>$ticket_details->description</td>
              </tr>
              <tr>
                <td>Reported By</td>
                <td>$ticket_details->created_staff_name</td>
              </tr>
              <tr>
                <td>Assigned To</td>
                <td>$ticket_details->assigned_to_staff_name</td>
              </tr>
              <tr>
                <td>Priority</td>
                <td>$ticket_details->priority_name</td>
              </tr>
              <tr>
                <td>Escalation Status</td>
                <td>$ticket_details->escalation_status</td>
              </tr>
              <tr>
                <td>Issue Type</td>
                <td>$ticket_details->issue_name</td>
              </tr>
              <tr>
                <td>Assigned Department</td>
                <td>$ticket_details->department_name</td>
              </tr>
            </table>
            <br>
            Thank you";
            $from_email = $this->settings->getSetting('internal_ticketing_from_email');
              $email_master_data = array(
              'subject' => "Ticket Resolved",
              'body' => $email_body,
              'source' => 'Internal Ticketing',
              'sent_by' => $this->authorization->getAvatarId(),
              'recievers' => 'Staff',
              'from_email' => $from_email,
              'files' => '',
              'acad_year_id' => $this->acad_year->getAcadYearId(),
              'visible' => 1,
              'sender_list' => empty($sender_list)?NULL:json_encode($sender_list),
              'sending_status' => 'Send Email'
          );
          $this->load->model('communication/emails_model');
          $email_master_id = $this->emails_model->saveEmail($email_master_data);
          $sent_data = (object) $emaildata;
          $status = $this->emails_model->save_sending_data($sent_data, $email_master_id);
          $email = $this->emails_model->getEmailInfo($email_master_id);
          $email_ids = [];
          if($val != '' && $val != null) {
            array_push($email_ids, $val);
          }
          sendEmail($email->body, $email->subject, $email_master_id, $email_ids, $email->from_email, json_decode($email->files));
        }
      }
    }
    echo $val;
  }

  public function remove_contributor(){
    $ticket_id = $_POST['ticket_id'];
    $staff_id = $_POST['staff_id'];
    echo ($this->Internalticketing_model->remove_contributor($ticket_id, $staff_id));
  }

  public function add_secondary_assignee(){
    echo ($this->Internalticketing_model->add_secondary_assignee());
  }

  public function update_ticket_assignee(){
    $ticket_id = $_POST['ticket_id'];
    $selected_staff = $_POST['selected_staff'];
    $previous_assigned_staff = $this->Internalticketing_model->get_ticket_assignee_by_id($ticket_id);
    $previous_assigned_staff_email = $this->Internalticketing_model->get_staff_name_and_email($ticket_id, 'assigned_to');
    $secondary_staff_emails = $this->Internalticketing_model->get_secondary_staff_emails($ticket_id);
    $val = $this->Internalticketing_model->update_ticket_assignee($ticket_id, $selected_staff);
    if ($val){
      $ticket_details = $this->Internalticketing_model->get_ticket_details_for_notification($ticket_id);
      $created_by_staff_name = $this->Internalticketing_model->get_staff_name_and_email($ticket_id, 'reported_by');
      $assigned_to_staff_name = $this->Internalticketing_model->get_staff_name_and_email($ticket_id, 'assigned_to');
      $staff_ids = $this->Internalticketing_model->get_ticket_assignee_by_id($ticket_id);
      if(!empty($staff_ids)) {
        $this->load->helper('texting_helper');
        $notify_array = array();
        $notify_array['staff_ids'] = $staff_ids;
        $notify_array['title'] = 'Ticket Assigned';
        $notify_array['message'] = "Ticket $ticket_details->ticket_item_id created by $created_by_staff_name->staff_name is assigned to you";
        $notify_array['mode'] = 'notification';
        $notify_array['source'] = 'Internal Ticketing';
        sendText($notify_array);
      }
      $sender_list = [];
      $emaildata = array();
      if(!empty($ticket_details)){
        $obj = new stdClass();
        $sender_list['staff'] = [
          'send_to_type' => 'Staff',
          'id' => $ticket_details->assigned_to,
        ];
        $obj->id = $ticket_details->assigned_to;
        $obj->email = $assigned_to_staff_name->email;
        $obj->avatar_type = '4';
        array_push($emaildata, $obj);
      }
      $email_body = 
      "Hi, Ticket $ticket_details->ticket_item_id created by $created_by_staff_name->staff_name is assigned to you. 
      Ticket details is as follows: <br><br>
      <table>
        <tr>
          <td>Ticket number</td>
          <td>$ticket_details->ticket_item_id</td>
        </tr>
        <tr>
          <td>Title</td>
          <td>$ticket_details->title</td>
        </tr>
        <tr>
          <td>Description</td>
          <td>$ticket_details->description</td>
        </tr>
        <tr>
          <td>Reported By</td>
          <td>$ticket_details->created_staff_name</td>
        </tr>
        <tr>
          <td>Assigned To</td>
          <td>$ticket_details->assigned_to_staff_name</td>
        </tr>
        <tr>
          <td>Priority</td>
          <td>$ticket_details->priority_name</td>
        </tr>
        <tr>
          <td>Escalation Status</td>
          <td>$ticket_details->escalation_status</td>
        </tr>
        <tr>
          <td>Issue Type</td>
          <td>$ticket_details->issue_name</td>
        </tr>
        <tr>
          <td>Assigned Department</td>
          <td>$ticket_details->department_name</td>
        </tr>
      </table>
      <br>
      Thank you";
      $from_email = $this->settings->getSetting('internal_ticketing_from_email');
      $email_master_data = array(
        'subject' => 'Ticket Assigned',
        'body' => $email_body,
        'source' => 'Internal Ticketing',
        'sent_by' => $this->authorization->getAvatarId(),
        'recievers' => 'Staff',
        'from_email' => $from_email,
        'files' => '',
        'acad_year_id' => $this->acad_year->getAcadYearId(),
        'visible' => 1,
        'sender_list' => empty($sender_list)?NULL:json_encode($sender_list),
        'sending_status' => 'Send Email'
      );
      $this->load->model('communication/emails_model');
      $email_master_id = $this->emails_model->saveEmail($email_master_data);
      $sent_data = (object) $emaildata;
      $status = $this->emails_model->save_sending_data($sent_data, $email_master_id);
      $email = $this->emails_model->getEmailInfo($email_master_id);
      $email_ids = [];
      if($assigned_to_staff_name->email != '' && $assigned_to_staff_name->email != null) {
        array_push($email_ids, $assigned_to_staff_name->email);
      }
      sendEmail($email->body, $email->subject, $email_master_id, $email_ids, $email->from_email, json_decode($email->files));

      if(!empty($previous_assigned_staff)) {
        $this->load->helper('texting_helper');
        $notify_array = array();
        $notify_array['staff_ids'] = $previous_assigned_staff;
        $notify_array['title'] = 'Ticket Reassigned';
        $notify_array['message'] = "Ticket $ticket_details->ticket_item_id with title '$ticket_details->title' assigned to you is reassigned to $assigned_to_staff_name->staff_name";
        $notify_array['mode'] = 'notification';
        $notify_array['source'] = 'Internal Ticketing';
        sendText($notify_array);
      }
      $sender_list = [];
      $emaildata = array();
      if(!empty($ticket_details)){
        $obj = new stdClass();
        $sender_list['staff'] = [
          'send_to_type' => 'Staff',
          'id' => $previous_assigned_staff_email->assigned_to,
        ];
        $obj->id = $previous_assigned_staff_email->assigned_to;
        $obj->email = $previous_assigned_staff_email->email;
        $obj->avatar_type = '4';
        array_push($emaildata, $obj);
      }

      $email_body = 
      "Hi, Ticket $ticket_details->ticket_item_id with title '$ticket_details->title' assigned to you is reassigned to $assigned_to_staff_name->staff_name. 
      Ticket details is as follows: <br><br>
      <table>
        <tr>
          <td>Ticket number</td>
          <td>$ticket_details->ticket_item_id</td>
        </tr>
        <tr>
          <td>Title</td>
          <td>$ticket_details->title</td>
        </tr>
        <tr>
          <td>Description</td>
          <td>$ticket_details->description</td>
        </tr>
        <tr>
          <td>Reported By</td>
          <td>$ticket_details->created_staff_name</td>
        </tr>
        <tr>
          <td>Assigned To</td>
          <td>$ticket_details->assigned_to_staff_name</td>
        </tr>
        <tr>
          <td>Priority</td>
          <td>$ticket_details->priority_name</td>
        </tr>
        <tr>
          <td>Escalation Status</td>
          <td>$ticket_details->escalation_status</td>
        </tr>
        <tr>
          <td>Issue Type</td>
          <td>$ticket_details->issue_name</td>
        </tr>
        <tr>
          <td>Assigned Department</td>
          <td>$ticket_details->department_name</td>
        </tr>
      </table>
      <br>
      Thank you";
      $from_email = $this->settings->getSetting('internal_ticketing_from_email');
      $email_master_data = array(
        'subject' => 'Ticket Reassigned',
        'body' => $email_body,
        'source' => 'Internal Ticketing',
        'sent_by' => $this->authorization->getAvatarId(),
        'recievers' => 'Staff',
        'from_email' => $from_email,
        'files' => '',
        'acad_year_id' => $this->acad_year->getAcadYearId(),
        'visible' => 1,
        'sender_list' => empty($sender_list)?NULL:json_encode($sender_list),
        'sending_status' => 'Send Email'
      );
      $this->load->model('communication/emails_model');
      $email_master_id = $this->emails_model->saveEmail($email_master_data);
      $sent_data = (object) $emaildata;
      $status = $this->emails_model->save_sending_data($sent_data, $email_master_id);
      $email = $this->emails_model->getEmailInfo($email_master_id);
      $email_ids = [];
      if($previous_assigned_staff_email->email != '' && $previous_assigned_staff_email->email != null) {
        array_push($email_ids, $previous_assigned_staff_email->email);
      }
      sendEmail($email->body, $email->subject, $email_master_id, $email_ids, $email->from_email, json_decode($email->files));

      $staff_ids = $this->Internalticketing_model->get_ticket_creator_by_id($ticket_id);
      if(!empty($staff_ids)) {
        $this->load->helper('texting_helper');
        $notify_array = array();
        $notify_array['staff_ids'] = $staff_ids;
        $notify_array['title'] = 'Ticket Reassigned';
        $notify_array['message'] = "Ticket $ticket_details->ticket_item_id with title '$ticket_details->title' created by you is reassigned to $assigned_to_staff_name->staff_name";
        $notify_array['mode'] = 'notification';
        $notify_array['source'] = 'Internal Ticketing';
        sendText($notify_array);
      }

      $sender_list = [];
      $emaildata = array();
      if(!empty($ticket_details)){
        $obj = new stdClass();
        $sender_list['staff'] = [
          'send_to_type' => 'Staff',
          'id' => $created_by_staff_name->reported_by,
        ];
        $obj->id = $created_by_staff_name->reported_by;
        $obj->email = $created_by_staff_name->email;
        $obj->avatar_type = '4';
        array_push($emaildata, $obj);
      }
      $email_body = 
      "Hi, Ticket $ticket_details->ticket_item_id with title '$ticket_details->title' created by you is reassigned to $assigned_to_staff_name->staff_name. 
      Ticket details is as follows: <br><br>
      <table>
        <tr>
          <td>Ticket number</td>
          <td>$ticket_details->ticket_item_id</td>
        </tr>
        <tr>
          <td>Title</td>
          <td>$ticket_details->title</td>
        </tr>
        <tr>
          <td>Description</td>
          <td>$ticket_details->description</td>
        </tr>
        <tr>
          <td>Reported By</td>
          <td>$ticket_details->created_staff_name</td>
        </tr>
        <tr>
          <td>Assigned To</td>
          <td>$ticket_details->assigned_to_staff_name</td>
        </tr>
        <tr>
          <td>Priority</td>
          <td>$ticket_details->priority_name</td>
        </tr>
        <tr>
          <td>Escalation Status</td>
          <td>$ticket_details->escalation_status</td>
        </tr>
        <tr>
          <td>Issue Type</td>
          <td>$ticket_details->issue_name</td>
        </tr>
        <tr>
          <td>Assigned Department</td>
          <td>$ticket_details->department_name</td>
        </tr>
      </table>
      <br>
      Thank you";
      $from_email = $this->settings->getSetting('internal_ticketing_from_email');
      $email_master_data = array(
        'subject' => 'Ticket Reassigned',
        'body' => $email_body,
        'source' => 'Internal Ticketing',
        'sent_by' => $this->authorization->getAvatarId(),
        'recievers' => 'Staff',
        'from_email' => $from_email,
        'files' => '',
        'acad_year_id' => $this->acad_year->getAcadYearId(),
        'visible' => 1,
        'sender_list' => empty($sender_list)?NULL:json_encode($sender_list),
        'sending_status' => 'Send Email'
      );
      $this->load->model('communication/emails_model');
      $email_master_id = $this->emails_model->saveEmail($email_master_data);
      $sent_data = (object) $emaildata;
      $status = $this->emails_model->save_sending_data($sent_data, $email_master_id);
      $email = $this->emails_model->getEmailInfo($email_master_id);
      $email_ids = [];
      if($created_by_staff_name->email != '' && $created_by_staff_name->email != null) {
        array_push($email_ids, $created_by_staff_name->email);
      }
      sendEmail($email->body, $email->subject, $email_master_id, $email_ids, $email->from_email, json_decode($email->files));

      $secondary_staff_ids = $this->Internalticketing_model->get_secondary_staff_ids($ticket_id);
      #notification to secondary assignees
      if(!empty($secondary_staff_ids)) {
        $this->load->helper('texting_helper');
        $notify_array = array();
        $notify_array['staff_ids'] = $secondary_staff_ids;
        $notify_array['title'] = 'Ticket Reassigned';
        $notify_array['message'] = "Ticket $ticket_details->ticket_item_id with title '$ticket_details->title' is reassigned to $assigned_to_staff_name->staff_name";
        $notify_array['mode'] = 'notification';
        $notify_array['source'] = 'Internal Ticketing';
        sendText($notify_array);
      }

      #email to secondary assignees
      if(!empty($secondary_staff_emails)){
        foreach($secondary_staff_emails->staff_data as $key=>$val){
          $sender_list = [];
          $emaildata = array();
          if(!empty($ticket_details)){
            $obj = new stdClass();
            $sender_list['staff'] = [
              'send_to_type' => 'Staff',
              'id' => $key,
            ];
            $obj->id = $key;
            $obj->email = $val;
            $obj->avatar_type = '4';
            array_push($emaildata, $obj);
          }
          $email_body = 
            "Hi, Ticket $ticket_details->ticket_item_id with title '$ticket_details->title' is reassigned to $assigned_to_staff_name->staff_name. 
            Ticket details is as follows: <br><br>
            <table>
              <tr>
                <td>Ticket number</td>
                <td>$ticket_details->ticket_item_id</td>
              </tr>
              <tr>
                <td>Title</td>
                <td>$ticket_details->title</td>
              </tr>
              <tr>
                <td>Description</td>
                <td>$ticket_details->description</td>
              </tr>
              <tr>
                <td>Reported By</td>
                <td>$ticket_details->created_staff_name</td>
              </tr>
              <tr>
                <td>Assigned To</td>
                <td>$ticket_details->assigned_to_staff_name</td>
              </tr>
              <tr>
                <td>Priority</td>
                <td>$ticket_details->priority_name</td>
              </tr>
              <tr>
                <td>Escalation Status</td>
                <td>$ticket_details->escalation_status</td>
              </tr>
              <tr>
                <td>Issue Type</td>
                <td>$ticket_details->issue_name</td>
              </tr>
              <tr>
                <td>Assigned Department</td>
                <td>$ticket_details->department_name</td>
              </tr>
            </table>
            <br>
            Thank you";
            $from_email = $this->settings->getSetting('internal_ticketing_from_email');
              $email_master_data = array(
              'subject' => "Ticket Reassigned",
              'body' => $email_body,
              'source' => 'Internal Ticketing',
              'sent_by' => $this->authorization->getAvatarId(),
              'recievers' => 'Staff',
              'from_email' => $from_email,
              'files' => '',
              'acad_year_id' => $this->acad_year->getAcadYearId(),
              'visible' => 1,
              'sender_list' => empty($sender_list)?NULL:json_encode($sender_list),
              'sending_status' => 'Send Email'
          );
          $this->load->model('communication/emails_model');
          $email_master_id = $this->emails_model->saveEmail($email_master_data);
          $sent_data = (object) $emaildata;
          $status = $this->emails_model->save_sending_data($sent_data, $email_master_id);
          $email = $this->emails_model->getEmailInfo($email_master_id);
          $email_ids = [];
          if($val != '' && $val != null) {
            array_push($email_ids, $val);
          }
          sendEmail($email->body, $email->subject, $email_master_id, $email_ids, $email->from_email, json_decode($email->files));
        }
      }
    }
    echo $val;
  }

  public function update_ticket_issue_type(){
    $ticket_id = $_POST['ticket_id'];
    $selected_issue_type = $_POST['selected_issue_type'];
    $old_department_staff_emails = $this->Internalticketing_model->get_department_staff_emails($ticket_id);
    $old_department_staff_ids = $this->Internalticketing_model->get_staff_by_department($ticket_id);
    $secondary_staff_emails = $this->Internalticketing_model->get_secondary_staff_emails($ticket_id);
    $val = $this->Internalticketing_model->update_ticket_issue_type($ticket_id, $selected_issue_type);
    if($val){
      $new_department_staff_emails = $this->Internalticketing_model->get_department_staff_emails($ticket_id);
      $ticket_details = $this->Internalticketing_model->get_ticket_details_for_notification($ticket_id); 
      $new_department_staff_ids = $this->Internalticketing_model->get_staff_by_department($ticket_id);
      if(!empty($old_department_staff_ids)) {
        $this->load->helper('texting_helper');
        $notify_array = array();
        $notify_array['staff_ids'] = $old_department_staff_ids;
        $notify_array['title'] = 'Ticket Reassigned';
        $notify_array['message'] = "Ticket $ticket_details->ticket_item_id with title '$ticket_details->title' assigned to your department is reassigned to $ticket_details->department_name";
        $notify_array['mode'] = 'notification';
        $notify_array['source'] = 'Internal Ticketing';
        sendText($notify_array);
      }
      foreach($old_department_staff_emails->staff_data as $key=>$val){
        $sender_list = [];
        $emaildata = array();
        if(!empty($ticket_details)){
          $obj = new stdClass();
          $sender_list['staff'] = [
            'send_to_type' => 'Staff',
            'id' => $key,
          ];
          $obj->id = $key;
          $obj->email = $val;
          $obj->avatar_type = '4';
          array_push($emaildata, $obj);
        }
        $email_body = 
        "Hi, Ticket $ticket_details->ticket_item_id with title '$ticket_details->title' assigned to your department is reassigned to $ticket_details->department_name. 
        Ticket details is as follows: <br><br>
        <table>
          <tr>
            <td>Ticket number</td>
            <td>$ticket_details->ticket_item_id</td>
          </tr>
          <tr>
            <td>Title</td>
            <td>$ticket_details->title</td>
          </tr>
          <tr>
            <td>Description</td>
            <td>$ticket_details->description</td>
          </tr>
          <tr>
            <td>Reported By</td>
            <td>$ticket_details->created_staff_name</td>
          </tr>
          <tr>
            <td>Assigned To</td>
            <td>$ticket_details->assigned_to_staff_name</td>
          </tr>
          <tr>
            <td>Priority</td>
            <td>$ticket_details->priority_name</td>
          </tr>
          <tr>
            <td>Escalation Status</td>
            <td>$ticket_details->escalation_status</td>
          </tr>
          <tr>
            <td>Issue Type</td>
            <td>$ticket_details->issue_name</td>
          </tr>
          <tr>
            <td>Assigned Department</td>
            <td>$ticket_details->department_name</td>
          </tr>
        </table>
        <br>
        Thank you";
        $from_email = $this->settings->getSetting('internal_ticketing_from_email');
        $email_master_data = array(
          'subject' => 'Ticket Reassigned',
          'body' => $email_body,
          'source' => 'Internal Ticketing',
          'sent_by' => $this->authorization->getAvatarId(),
          'recievers' => 'Staff',
          'from_email' => $from_email,
          'files' => '',
          'acad_year_id' => $this->acad_year->getAcadYearId(),
          'visible' => 1,
          'sender_list' => empty($sender_list)?NULL:json_encode($sender_list),
          'sending_status' => 'Send Email'
        );
        $this->load->model('communication/emails_model');
        $email_master_id = $this->emails_model->saveEmail($email_master_data);
        $sent_data = (object) $emaildata;
        $status = $this->emails_model->save_sending_data($sent_data, $email_master_id);
        $email = $this->emails_model->getEmailInfo($email_master_id);
        $email_ids = [];
        if($val != '' && $val != null) {
          array_push($email_ids, $val);
        }
        sendEmail($email->body, $email->subject, $email_master_id, $email_ids, $email->from_email, json_decode($email->files));
      }
      
      if(!empty($new_department_staff_ids)) {
        $this->load->helper('texting_helper');
        $notify_array = array();
        $notify_array['staff_ids'] = $new_department_staff_ids;
        $notify_array['title'] = 'Ticket Reassigned';
        $notify_array['message'] = "Ticket $ticket_details->ticket_item_id with title '$ticket_details->title' is reassigned to your department";
        $notify_array['mode'] = 'notification';
        $notify_array['source'] = 'Internal Ticketing';
        sendText($notify_array);
      }
      foreach($new_department_staff_emails->staff_data as $key=>$val){
        $sender_list = [];
        $emaildata = array();
        if(!empty($ticket_details)){
          $obj = new stdClass();
          $sender_list['staff'] = [
            'send_to_type' => 'Staff',
            'id' => $key,
          ];
          $obj->id = $key;
          $obj->email = $val;
          $obj->avatar_type = '4';
          array_push($emaildata, $obj);
        }
        $email_body = 
        "Hi, Ticket $ticket_details->ticket_item_id with title '$ticket_details->title' assigned to your department is reassigned to $ticket_details->department_name. 
        Ticket details is as follows: <br><br>
        <table>
          <tr>
            <td>Ticket number</td>
            <td>$ticket_details->ticket_item_id</td>
          </tr>
          <tr>
            <td>Title</td>
            <td>$ticket_details->title</td>
          </tr>
          <tr>
            <td>Description</td>
            <td>$ticket_details->description</td>
          </tr>
          <tr>
            <td>Reported By</td>
            <td>$ticket_details->created_staff_name</td>
          </tr>
          <tr>
            <td>Assigned To</td>
            <td>$ticket_details->assigned_to_staff_name</td>
          </tr>
          <tr>
            <td>Priority</td>
            <td>$ticket_details->priority_name</td>
          </tr>
          <tr>
            <td>Escalation Status</td>
            <td>$ticket_details->escalation_status</td>
          </tr>
          <tr>
            <td>Issue Type</td>
            <td>$ticket_details->issue_name</td>
          </tr>
          <tr>
            <td>Assigned Department</td>
            <td>$ticket_details->department_name</td>
          </tr>
        </table>
        <br>
        Thank you";
        $from_email = $this->settings->getSetting('internal_ticketing_from_email');
        $email_master_data = array(
          'subject' => 'Ticket Reassigned',
          'body' => $email_body,
          'source' => 'Internal Ticketing',
          'sent_by' => $this->authorization->getAvatarId(),
          'recievers' => 'Staff',
          'from_email' => $from_email,
          'files' => '',
          'acad_year_id' => $this->acad_year->getAcadYearId(),
          'visible' => 1,
          'sender_list' => empty($sender_list)?NULL:json_encode($sender_list),
          'sending_status' => 'Send Email'
        );
        $this->load->model('communication/emails_model');
        $email_master_id = $this->emails_model->saveEmail($email_master_data);
        $sent_data = (object) $emaildata;
        $status = $this->emails_model->save_sending_data($sent_data, $email_master_id);
        $email = $this->emails_model->getEmailInfo($email_master_id);
        $email_ids = [];
        if($val != '' && $val != null) {
          array_push($email_ids, $val);
        }
        sendEmail($email->body, $email->subject, $email_master_id, $email_ids, $email->from_email, json_decode($email->files));
      }

      $staff_ids = $this->Internalticketing_model->get_ticket_creator_by_id($ticket_id);
      if(!empty($staff_ids)) {
        $this->load->helper('texting_helper');
        $notify_array = array();
        $notify_array['staff_ids'] = $staff_ids;
        $notify_array['title'] = 'Ticket Reassigned';
        $notify_array['message'] = "Ticket $ticket_details->ticket_item_id with title '$ticket_details->title' created by you is reassigned to $ticket_details->department_name";
        $notify_array['mode'] = 'notification';
        $notify_array['source'] = 'Internal Ticketing';
        sendText($notify_array);
      }

      $sender_list = [];
      $emaildata = array();
      if(!empty($ticket_details)){
        $obj = new stdClass();
        $sender_list['staff'] = [
          'send_to_type' => 'Staff',
          'id' => $ticket_details->reported_by,
        ];
        $obj->id = $ticket_details->reported_by;
        $obj->email = $ticket_details->created_by_staff_email;
        $obj->avatar_type = '4';
        array_push($emaildata, $obj);
      }
      $email_body = 
      "Hi, Ticket $ticket_details->ticket_item_id with title '$ticket_details->title' created by you is reassigned to $ticket_details->department_name. 
      Ticket details is as follows: <br><br>
      <table>
        <tr>
          <td>Ticket number</td>
          <td>$ticket_details->ticket_item_id</td>
        </tr>
        <tr>
          <td>Title</td>
          <td>$ticket_details->title</td>
        </tr>
        <tr>
          <td>Description</td>
          <td>$ticket_details->description</td>
        </tr>
        <tr>
          <td>Reported By</td>
          <td>$ticket_details->created_staff_name</td>
        </tr>
        <tr>
          <td>Assigned To</td>
          <td>$ticket_details->assigned_to_staff_name</td>
        </tr>
        <tr>
          <td>Priority</td>
          <td>$ticket_details->priority_name</td>
        </tr>
        <tr>
          <td>Escalation Status</td>
          <td>$ticket_details->escalation_status</td>
        </tr>
        <tr>
          <td>Issue Type</td>
          <td>$ticket_details->issue_name</td>
        </tr>
        <tr>
          <td>Assigned Department</td>
          <td>$ticket_details->department_name</td>
        </tr>
      </table>
      <br>
      Thank you";
      $from_email = $this->settings->getSetting('internal_ticketing_from_email');
      $email_master_data = array(
        'subject' => 'Ticket Reassigned',
        'body' => $email_body,
        'source' => 'Internal Ticketing',
        'sent_by' => $this->authorization->getAvatarId(),
        'recievers' => 'Staff',
        'from_email' => $from_email,
        'files' => '',
        'acad_year_id' => $this->acad_year->getAcadYearId(),
        'visible' => 1,
        'sender_list' => empty($sender_list)?NULL:json_encode($sender_list),
        'sending_status' => 'Send Email'
      );
      $this->load->model('communication/emails_model');
      $email_master_id = $this->emails_model->saveEmail($email_master_data);
      $sent_data = (object) $emaildata;
      $status = $this->emails_model->save_sending_data($sent_data, $email_master_id);
     
      $email = $this->emails_model->getEmailInfo($email_master_id);

      $email_ids = [];
      if($ticket_details->created_by_staff_email != '' && $ticket_details->created_by_staff_email != null) {
        array_push($email_ids, $ticket_details->created_by_staff_email);
      }
      sendEmail($email->body, $email->subject, $email_master_id, $email_ids, $email->from_email, json_decode($email->files));

      $secondary_staff_ids = $this->Internalticketing_model->get_secondary_staff_ids($ticket_id);
      #notification to secondary assignees
      if(!empty($secondary_staff_ids)) {
        $this->load->helper('texting_helper');
        $notify_array = array();
        $notify_array['staff_ids'] = $secondary_staff_ids;
        $notify_array['title'] = 'Ticket Reassigned';
        $notify_array['message'] = "Ticket $ticket_details->ticket_item_id with title '$ticket_details->title' is reassigned to $ticket_details->department_name";
        $notify_array['mode'] = 'notification';
        $notify_array['source'] = 'Internal Ticketing';
        sendText($notify_array);
      }

      #email to secondary assignees
      if(!empty($secondary_staff_emails)){
        foreach($secondary_staff_emails->staff_data as $key=>$val){
          $sender_list = [];
          $emaildata = array();
          if(!empty($ticket_details)){
            $obj = new stdClass();
            $sender_list['staff'] = [
              'send_to_type' => 'Staff',
              'id' => $key,
            ];
            $obj->id = $key;
            $obj->email = $val;
            $obj->avatar_type = '4';
            array_push($emaildata, $obj);
          }
          $email_body = 
            "Hi, Ticket $ticket_details->ticket_item_id with title '$ticket_details->title' is reassigned to $ticket_details->department_name. 
            Ticket details is as follows: <br><br>
            <table>
              <tr>
                <td>Ticket number</td>
                <td>$ticket_details->ticket_item_id</td>
              </tr>
              <tr>
                <td>Title</td>
                <td>$ticket_details->title</td>
              </tr>
              <tr>
                <td>Description</td>
                <td>$ticket_details->description</td>
              </tr>
              <tr>
                <td>Reported By</td>
                <td>$ticket_details->created_staff_name</td>
              </tr>
              <tr>
                <td>Assigned To</td>
                <td>$ticket_details->assigned_to_staff_name</td>
              </tr>
              <tr>
                <td>Priority</td>
                <td>$ticket_details->priority_name</td>
              </tr>
              <tr>
                <td>Escalation Status</td>
                <td>$ticket_details->escalation_status</td>
              </tr>
              <tr>
                <td>Issue Type</td>
                <td>$ticket_details->issue_name</td>
              </tr>
              <tr>
                <td>Assigned Department</td>
                <td>$ticket_details->department_name</td>
              </tr>
            </table>
            <br>
            Thank you";
            $from_email = $this->settings->getSetting('internal_ticketing_from_email');
              $email_master_data = array(
              'subject' => "Ticket Reassigned",
              'body' => $email_body,
              'source' => 'Internal Ticketing',
              'sent_by' => $this->authorization->getAvatarId(),
              'recievers' => 'Staff',
              'from_email' => $from_email,
              'files' => '',
              'acad_year_id' => $this->acad_year->getAcadYearId(),
              'visible' => 1,
              'sender_list' => empty($sender_list)?NULL:json_encode($sender_list),
              'sending_status' => 'Send Email'
          );
          $this->load->model('communication/emails_model');
          $email_master_id = $this->emails_model->saveEmail($email_master_data);
          $sent_data = (object) $emaildata;
          $status = $this->emails_model->save_sending_data($sent_data, $email_master_id);
          $email = $this->emails_model->getEmailInfo($email_master_id);
          $email_ids = [];
          if($val != '' && $val != null) {
            array_push($email_ids, $val);
          }
          sendEmail($email->body, $email->subject, $email_master_id, $email_ids, $email->from_email, json_decode($email->files));
        }
      }

    }
    echo $val;
  }

  public function get_ticket_status(){
    $ticket_id = $_POST['ticket_id'];
    $val = $this->Internalticketing_model->get_ticket_status($ticket_id);
    echo json_encode($val);
  }

  public function close_reopen_ticket(){
    $ticket_id = $_POST['ticket_id'];
    $closure_remarks = $_POST['closure_remarks'];
    $ticket_status = $_POST['ticket_status'];
    $closed_by = $_POST['closed_by'];
    $closure_rating = $_POST['rating'];
    $val = $this->Internalticketing_model->close_reopen_ticket($ticket_id, $closure_remarks, $ticket_status, $closed_by, $closure_rating);
    if ($val){
      $ticket_details = $this->Internalticketing_model->get_ticket_details_for_notification($ticket_id); 
      $close_reopen_by_staff_details = $this->Internalticketing_model->get_staff_name_and_email($ticket_id, "closed_by");
      $assignee_email = $this->Internalticketing_model->get_staff_name_and_email($ticket_id, "assigned_to");
      $secondary_staff_emails = $this->Internalticketing_model->get_secondary_staff_emails($ticket_id);
      $staff_ids = $this->Internalticketing_model->get_ticket_assignee_by_id($ticket_id);
      $secondary_staff_ids = $this->Internalticketing_model->get_secondary_staff_ids($ticket_id);

      if ($ticket_status == 'Reopen'){
        $ticket_status = "Reopened";
      }
      if(!empty($staff_ids)) {
        $this->load->helper('texting_helper');
        $notify_array = array();
        $notify_array['staff_ids'] = $staff_ids;
        $notify_array['title'] = "Ticket $ticket_status";
        $notify_array['message'] = "Ticket $ticket_details->ticket_item_id with '$ticket_details->title' assigned to you is $ticket_status by $close_reopen_by_staff_details->staff_name";
        $notify_array['mode'] = 'notification';
        $notify_array['source'] = 'Internal Ticketing';
        sendText($notify_array);
      }
      $sender_list = [];
      $emaildata = array();
      if(!empty($ticket_details)){
        $obj = new stdClass();
        $sender_list['staff'] = [
          'send_to_type' => 'Staff',
          'id' => $ticket_details->assigned_to,
        ];
        $obj->id = $assignee_email->assigned_to;
        $obj->email = $assignee_email->email;
        $obj->avatar_type = '4';
        array_push($emaildata, $obj);
      }
      $email_body = 
      "Hi, Ticket $ticket_details->ticket_item_id with '$ticket_details->title' assigned to you is $ticket_status by $close_reopen_by_staff_details->staff_name. 
      Ticket details is as follows: <br><br>
      <table>
        <tr>
          <td>Ticket number</td>
          <td>$ticket_details->ticket_item_id</td>
        </tr>
        <tr>
          <td>Title</td>
          <td>$ticket_details->title</td>
        </tr>
        <tr>
          <td>Description</td>
          <td>$ticket_details->description</td>
        </tr>
        <tr>
          <td>Reported By</td>
          <td>$ticket_details->created_staff_name</td>
        </tr>
        <tr>
          <td>Assigned To</td>
          <td>$ticket_details->assigned_to_staff_name</td>
        </tr>
        <tr>
          <td>Priority</td>
          <td>$ticket_details->priority_name</td>
        </tr>
        <tr>
          <td>Escalation Status</td>
          <td>$ticket_details->escalation_status</td>
        </tr>
        <tr>
          <td>Issue Type</td>
          <td>$ticket_details->issue_name</td>
        </tr>
        <tr>
          <td>Assigned Department</td>
          <td>$ticket_details->department_name</td>
        </tr>
      </table>
      <br>
      Thank you";
      $from_email = $this->settings->getSetting('internal_ticketing_from_email');
        $email_master_data = array(
        'subject' => "Ticket $ticket_status",
        'body' => $email_body,
        'source' => 'Internal Ticketing',
        'sent_by' => $this->authorization->getAvatarId(),
        'recievers' => 'Staff',
        'from_email' => $from_email,
        'files' => '',
        'acad_year_id' => $this->acad_year->getAcadYearId(),
        'visible' => 1,
        'sender_list' => empty($sender_list)?NULL:json_encode($sender_list),
        'sending_status' => 'Send Email'
      );
      $this->load->model('communication/emails_model');
      $email_master_id = $this->emails_model->saveEmail($email_master_data);
      $sent_data = (object) $emaildata;
      $status = $this->emails_model->save_sending_data($sent_data, $email_master_id);
    
      $email = $this->emails_model->getEmailInfo($email_master_id);

      $email_ids = [];
      if($close_reopen_by_staff_details->email != '' && $close_reopen_by_staff_details->email != null) {
        array_push($email_ids, $close_reopen_by_staff_details->email);
      }
      sendEmail($email->body, $email->subject, $email_master_id, $email_ids, $email->from_email, json_decode($email->files));

      #notification to secondary assignee
      if(!empty($secondary_staff_ids)) {
        $this->load->helper('texting_helper');
        $notify_array = array();
        $notify_array['staff_ids'] = $secondary_staff_ids;
        $notify_array['title'] = "Ticket $ticket_status";
        $notify_array['message'] = "Ticket $ticket_details->ticket_item_id with '$ticket_details->title' is $ticket_status by $close_reopen_by_staff_details->staff_name";
        $notify_array['mode'] = 'notification';
        $notify_array['source'] = 'Internal Ticketing';
        sendText($notify_array);
      }

      #email to secondary assignees
      if(!empty($secondary_staff_emails)){
        foreach($secondary_staff_emails->staff_data as $key=>$val){
          $sender_list = [];
          $emaildata = array();
          if(!empty($ticket_details)){
            $obj = new stdClass();
            $sender_list['staff'] = [
              'send_to_type' => 'Staff',
              'id' => $key,
            ];
            $obj->id = $key;
            $obj->email = $val;
            $obj->avatar_type = '4';
            array_push($emaildata, $obj);
          }
          $email_body = 
            "Hi, Ticket $ticket_details->ticket_item_id with '$ticket_details->title' is $ticket_status by $close_reopen_by_staff_details->staff_name. 
            Ticket details is as follows: <br><br>
            <table>
              <tr>
                <td>Ticket number</td>
                <td>$ticket_details->ticket_item_id</td>
              </tr>
              <tr>
                <td>Title</td>
                <td>$ticket_details->title</td>
              </tr>
              <tr>
                <td>Description</td>
                <td>$ticket_details->description</td>
              </tr>
              <tr>
                <td>Reported By</td>
                <td>$ticket_details->created_staff_name</td>
              </tr>
              <tr>
                <td>Assigned To</td>
                <td>$ticket_details->assigned_to_staff_name</td>
              </tr>
              <tr>
                <td>Priority</td>
                <td>$ticket_details->priority_name</td>
              </tr>
              <tr>
                <td>Escalation Status</td>
                <td>$ticket_details->escalation_status</td>
              </tr>
              <tr>
                <td>Issue Type</td>
                <td>$ticket_details->issue_name</td>
              </tr>
              <tr>
                <td>Assigned Department</td>
                <td>$ticket_details->department_name</td>
              </tr>
            </table>
            <br>
            Thank you";
            $from_email = $this->settings->getSetting('internal_ticketing_from_email');
              $email_master_data = array(
              'subject' => "Ticket $ticket_status",
              'body' => $email_body,
              'source' => 'Internal Ticketing',
              'sent_by' => $this->authorization->getAvatarId(),
              'recievers' => 'Staff',
              'from_email' => $from_email,
              'files' => '',
              'acad_year_id' => $this->acad_year->getAcadYearId(),
              'visible' => 1,
              'sender_list' => empty($sender_list)?NULL:json_encode($sender_list),
              'sending_status' => 'Send Email'
          );
          $this->load->model('communication/emails_model');
          $email_master_id = $this->emails_model->saveEmail($email_master_data);
          $sent_data = (object) $emaildata;
          $status = $this->emails_model->save_sending_data($sent_data, $email_master_id);
          $email = $this->emails_model->getEmailInfo($email_master_id);
          $email_ids = [];
          if($val != '' && $val != null) {
            array_push($email_ids, $val);
          }
          sendEmail($email->body, $email->subject, $email_master_id, $email_ids, $email->from_email, json_decode($email->files));
        }
      }

    }
    echo $val;
  }

  public function get_resolution_details($ticket_id){
    $val = $this->Internalticketing_model->get_resolution_details($ticket_id);
    echo json_encode($val);
  }

  #***********************Comments******************

  public function get_comments(){
    $ticket_id = $_POST['ticket_id'];
    $val = $this->Internalticketing_model->get_comments($ticket_id);
    echo json_encode($val);
  }

  public function insert_comment(){
    $ticket_id = $_POST['ticket_id'];
    $val = $this->Internalticketing_model->insert_comment();
    if ($val){
      $ticket_details = $this->Internalticketing_model->get_ticket_details_for_notification($ticket_id);
      $assignee_email = $this->Internalticketing_model->get_staff_name_and_email($ticket_id, 'assigned_to');
      $secondary_staff_emails = $this->Internalticketing_model->get_secondary_staff_emails($ticket_id);
      $staff_ids = $this->Internalticketing_model->get_ticket_creator_assignee_by_id($ticket_id);
      $secondary_staff_ids = $this->Internalticketing_model->get_secondary_staff_ids($ticket_id);

      if(!empty($staff_ids)) {
        $this->load->helper('texting_helper');
        $notify_array = array();
        $notify_array['staff_ids'] = $staff_ids;
        $notify_array['title'] = 'Comment Added';
        $notify_array['message'] = "A comment is added to ticket $ticket_details->ticket_item_id";
        $notify_array['mode'] = 'notification';
        $notify_array['source'] = 'Internal Ticketing';
        sendText($notify_array);
      }
      
      $memberEmail = [];
      $sender_list = [];
      $emaildata = array();
      if(!empty($ticket_details)){
        $obj = new stdClass();
        $sender_list['staff'] = [
          'send_to_type' => 'Staff',
          'id' => $assignee_email->assigned_to,
        ];
        
        $obj->id = $assignee_email->assigned_to;
        $obj->assignee_email = $assignee_email->email;
        $obj->creator_email = $ticket_details->created_by_staff_email;
        $obj->avatar_type = '4';
        array_push($emaildata, $obj);
      }
      $email_body = 
      "Hi, A comment is added to ticket $ticket_details->ticket_item_id. 
      Ticket details is as follows: <br><br>
      <table>
        <tr>
          <td>Ticket number</td>
          <td>$ticket_details->ticket_item_id</td>
        </tr>
        <tr>
          <td>Title</td>
          <td>$ticket_details->title</td>
        </tr>
        <tr>
          <td>Description</td>
          <td>$ticket_details->description</td>
        </tr>
        <tr>
          <td>Reported By</td>
          <td>$ticket_details->created_staff_name</td>
        </tr>
        <tr>
          <td>Assigned To</td>
          <td>$ticket_details->assigned_to_staff_name</td>
        </tr>
        <tr>
          <td>Priority</td>
          <td>$ticket_details->priority_name</td>
        </tr>
        <tr>
          <td>Escalation Status</td>
          <td>$ticket_details->escalation_status</td>
        </tr>
        <tr>
          <td>Issue Type</td>
          <td>$ticket_details->issue_name</td>
        </tr>
        <tr>
          <td>Assigned Department</td>
          <td>$ticket_details->department_name</td>
        </tr>
      </table>
      <br>
      Thank you";
      $from_email = $this->settings->getSetting('internal_ticketing_from_email');
        $email_master_data = array(
        'subject' => "Comment Added",
        'body' => $email_body,
        'source' => 'Internal Ticketing',
        'sent_by' => $this->authorization->getAvatarId(),
        'recievers' => 'Staff',
        'from_email' => $from_email,
        'files' => '',
        'acad_year_id' => $this->acad_year->getAcadYearId(),
        'visible' => 1,
        'sender_list' => empty($sender_list)?NULL:json_encode($sender_list),
        'sending_status' => 'Send Email'
      );
      $this->load->model('communication/emails_model');
      $email_master_id = $this->emails_model->saveEmail($email_master_data);
      $sent_data = (object) $emaildata;
      $status = $this->emails_model->save_sending_data($sent_data, $email_master_id);
    
      $email = $this->emails_model->getEmailInfo($email_master_id);

      $email_ids = [];
      if($ticket_details->created_by_staff_email != '' && $ticket_details->created_by_staff_email != null || $assignee_email->email != '' && $assignee_email->email != null) {
        array_push($email_ids, $assignee_email->email);
        array_push($email_ids, $ticket_details->created_by_staff_email);

      }
      // print_r($email_master_data);
      // print_r($email_ids);die();
      sendEmail($email->body, $email->subject, $email_master_id, $email_ids, $email->from_email, json_decode($email->files));

      #notification to secondary assignees
      if(!empty($secondary_staff_ids)) {
        $this->load->helper('texting_helper');
        $notify_array = array();
        $notify_array['staff_ids'] = $secondary_staff_ids;
        $notify_array['title'] = 'Comment Added';
        $notify_array['message'] = "A comment is added to ticket $ticket_details->ticket_item_id";
        $notify_array['mode'] = 'notification';
        $notify_array['source'] = 'Internal Ticketing';
        sendText($notify_array);
      }

      #email to secondary assignees
      if(!empty($secondary_staff_emails)){
        foreach($secondary_staff_emails->staff_data as $key=>$val){
          $sender_list = [];
          $emaildata = array();
          if(!empty($ticket_details)){
            $obj = new stdClass();
            $sender_list['staff'] = [
              'send_to_type' => 'Staff',
              'id' => $key,
            ];
            $obj->id = $key;
            $obj->email = $val;
            $obj->avatar_type = '4';
            array_push($emaildata, $obj);
          }
          $email_body = 
            "Hi, A comment is added to ticket $ticket_details->ticket_item_id. 
            Ticket details is as follows: <br><br>
            <table>
              <tr>
                <td>Ticket number</td>
                <td>$ticket_details->ticket_item_id</td>
              </tr>
              <tr>
                <td>Title</td>
                <td>$ticket_details->title</td>
              </tr>
              <tr>
                <td>Description</td>
                <td>$ticket_details->description</td>
              </tr>
              <tr>
                <td>Reported By</td>
                <td>$ticket_details->created_staff_name</td>
              </tr>
              <tr>
                <td>Assigned To</td>
                <td>$ticket_details->assigned_to_staff_name</td>
              </tr>
              <tr>
                <td>Priority</td>
                <td>$ticket_details->priority_name</td>
              </tr>
              <tr>
                <td>Escalation Status</td>
                <td>$ticket_details->escalation_status</td>
              </tr>
              <tr>
                <td>Issue Type</td>
                <td>$ticket_details->issue_name</td>
              </tr>
              <tr>
                <td>Assigned Department</td>
                <td>$ticket_details->department_name</td>
              </tr>
            </table>
            <br>
            Thank you";
            $from_email = $this->settings->getSetting('internal_ticketing_from_email');
              $email_master_data = array(
              'subject' => "Ticket $ticket_status",
              'body' => $email_body,
              'source' => 'Internal Ticketing',
              'sent_by' => $this->authorization->getAvatarId(),
              'recievers' => 'Staff',
              'from_email' => $from_email,
              'files' => '',
              'acad_year_id' => $this->acad_year->getAcadYearId(),
              'visible' => 1,
              'sender_list' => empty($sender_list)?NULL:json_encode($sender_list),
              'sending_status' => 'Send Email'
          );
          $this->load->model('communication/emails_model');
          $email_master_id = $this->emails_model->saveEmail($email_master_data);
          $sent_data = (object) $emaildata;
          $status = $this->emails_model->save_sending_data($sent_data, $email_master_id);
          $email = $this->emails_model->getEmailInfo($email_master_id);
          $email_ids = [];
          if($val != '' && $val != null) {
            array_push($email_ids, $val);
          }
          sendEmail($email->body, $email->subject, $email_master_id, $email_ids, $email->from_email, json_decode($email->files));
        }
      }
    }
    echo ($val);
  }

  public function ticket_analysis(){
    $data['department_list'] = $this->Internalticketing_model->get_department_list();
    $data['main_content'] = 'internal_ticketing/ticket_analysis';
    $this->load->view('inc/template', $data);
  }

  public function weekly_report(){
    $data['staff_list'] = $this->Internalticketing_model->get_all_staff();
    $data['staff_issues_types'] = $this->Internalticketing_model->get_issues();
    $data['main_content'] = 'internal_ticketing/weekly_report';
    $this->load->view('inc/template', $data);
  }

  public function getWeeklyReport(){
    $data = $this->Internalticketing_model->get_weekly_report();
    echo json_encode($data);
  }

  public function get_report_by_date(){
    $data = $this->Internalticketing_model->get_report_by_date();
    echo json_encode($data);
  }

  public function report(){
    $data['staff_list'] = $this->Internalticketing_model->get_all_staff();
    $data['staff_issues_types'] = $this->Internalticketing_model->get_issues();
    $data['main_content'] = 'internal_ticketing/report';
    $this->load->view('inc/template', $data);
  }

  public function getReport(){
    $data = $this->Internalticketing_model->get_report();
    echo json_encode($data);
  }
  
  public function get_open_ticket_count(){
    $data = $this->Internalticketing_model->get_open_ticket_count();
    echo json_encode($data);
  }

  public function get_resolved_ticket_count(){
    $data = $this->Internalticketing_model->get_resolved_ticket_count();
    echo json_encode($data);
  }

  public function get_top_resolver(){
    $data = $this->Internalticketing_model->get_top_resolver();
    echo json_encode($data);
  }

  public function get_top_reporter(){
    $data = $this->Internalticketing_model->get_top_reporter();
    echo json_encode($data);
  }

  public function get_department_wise_created_resolved_data(){
    $data = $this->Internalticketing_model->get_ticket_count_department_wise();
    echo json_encode($data);
  }

  public function get_department_wise_priority_data(){
    $result = $this->Internalticketing_model->get_department_wise_priority_data();
    echo json_encode($result);
  }

  public function get_closure_rating_data(){
    $result = $this->Internalticketing_model->get_closure_rating_data();
    echo json_encode($result);
  }

  public function get_internal_ticket_summary(){
    $fromDate = $_POST['from_date'];
    $department = $_POST['department'];
    $toDate = $_POST['to_date'];
    $result['trend'] = $this->Internalticketing_model->get_internal_ticket_summary($fromDate, $toDate, $department);
    $result['count'] = $this->Internalticketing_model->get_ticket_trend_count($fromDate, $toDate, $department);
    echo json_encode($result);
  }

  public function get_weekly_internal_ticket_summary(){
    $department = $_POST['department'];
    $result = $this->Internalticketing_model->get_weekly_internal_ticket_summary($department);
    echo json_encode($result);
  }

  public function get_resolution_time_of_tickets(){
    $department = $_POST['department'];
    $result = $this->Internalticketing_model->get_tickets_with_resolution_time($department);
    echo json_encode($result);
  }
  

  
}