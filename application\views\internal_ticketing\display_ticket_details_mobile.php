<div class="card panel_new_style">
    <div class="card-header panel_heading_new_style_padding" style="padding-top: 10px;">
   
        <h3 class="card-title panel_title_new_style">
        <a class="back_anchor" href='<?php echo site_url("internal_ticketing"); ?>'>
          <span class="fa fa-arrow-left"></span>
        </a>
            <strong>Ticket Details</strong>
            <!-- <a href="<?php //echo site_url('internal_ticketing/create_ticket');?>" class="new_circleShape_res" style="background-color: #fe970a;" >
                <span class="fa fa-plus" style="font-size: 19px;"></span>
              </a> -->
        </h3>
    </div>
</div>
<div class="card-body px-2 py-1">
    <div id="ticketDetails">
        <?php $stakeholder_id = $this->authorization->getAvatarStakeholderId(); ?>
        <input type="hidden" id="department_id" name="department_id" value="<?php echo $ticket_data->assigned_to_department_id ?>" >
        <table class="table table-bordered">
        <tr>
        <td style="width:150px;">
        <span><strong>Ticket Number</strong></span>
        </td>
        <td><?php echo $ticket_data->ticket_item_id?></td>
        </tr>

        <tr>
        <td style="width:150px;">
        <span><strong>Title</strong></span>
        </td>
        <td id="title" ><?php echo $ticket_data->title?>'</td>
        </tr>

        <tr>
        <td style="width:150px;"><strong>Reported By</strong></td>
        <td><?php echo $ticket_data->reported_by?></td>
        </tr>

        <tr>
        <td style="width:150px;"><strong>Created Date </strong></td>
        <td><?php echo $ticket_data->created_on?></td>
        </tr>

        <tr>
        <td style="width: 50px;">
        <span><strong>Description </strong></span>
        </td>
        <?php if(empty( $ticket_data->description)){?>
          <td>-</td>
        <?php }else{ ?>
          <td class="text-left" id="description"><?php echo $ticket_data->description?></td>
        <?php } ?>    
        </tr>

        <tr>
        <td style="width:150px;"><strong>Priority</strong></td>
        <td><?php echo $ticket_data->priority_name?>&nbsp;&nbsp;<button class="btn btn-primary" id="view_priority_details" type="button" data-toggle="modal" data-target="#view_priority_details_modal"  onclick="view_priority_details(<?php echo $ticket_data->ticket_id?>)">View Details</button></td>
        </tr>

        <tr>
        <td style="width:150px;"><strong>Escalation status </strong></td>
        <td><?php echo $ticket_data->escalation_status?></td>
        </tr>

        <tr>
        <td style="width:150px;"><strong>Ticket status</strong></td>
        <?php $assigned_to = $ticket_data->assigned_to; ?>
        <?php if ($ticket_data->ticket_status == 'Open' && $stakeholder_id == $assigned_to){?>
        <?php $details_tab = "details_tab"; ?>
        <td><b style="font-size: 17px"><?php echo $ticket_data->ticket_status?> &nbsp;&nbsp;<button class="btn btn-primary" id="in_progress" type="button" onclick="update_to_in_progress(<?php echo $ticket_data->ticket_id ?>, '<?php echo $ticket_data->ticket_status?>')">Mark as "In Progress"</button></b></td>`;
        <?php }else if($ticket_data->ticket_status == 'Open'){ ?>
        <td><b style="font-size: 17px"><?php echo $ticket_data->ticket_status; ?>
        &nbsp;&nbsp;<button style="pointer-events: none;" class="btn btn-primary" type="button" disabled>Mark as "In Progress"</button></b><br><help style="color: #d1c6c6">Only Ticket Assignee can mark the ticket as In Progress</help></td>
        <?php }else{ ?>
        <td><b style="font-size: 17px"><?php echo $ticket_data->ticket_status; ?></td>
        <?php } ?>
        </tr>

        <tr>
        <td style="width:150px;"><strong>Issue Type</strong></td>
        <?php if ($ticket_data->ticket_status == 'Open' || $ticket_data->ticket_status == 'In Progress' || $ticket_data->ticket_status == 'Reopen'){ ?>
        <td><?php echo $ticket_data->issue_name; ?>&nbsp;&nbsp;<a href=""  id="reassign_issue_type" class="btn btn-primary" data-toggle="modal" data-target="#reassign_issue_type_modal">Reassign</a></td>
        <?php }else{ ?>
        <td><?php echo $ticket_data->issue_name; ?></td>
        <?php } ?>
        </tr>

        <tr>
        <td style="width:150px;"><strong>Assigned Department </strong></td>
        <td><?php echo $ticket_data->department_name ?></td>
        </tr>

        <tr>
        <td style="width:150px;"><strong>Assigned To</strong></td>
        <?php if ($ticket_data->ticket_status == 'Open' || $ticket_data->ticket_status == 'In Progress' || $ticket_data->ticket_status == 'Reopen'){ ?>
        <td><?php echo $ticket_data->assigned_staff ?> &nbsp;&nbsp;<a href=""  id="reassign_assignee" class="btn btn-primary" data-toggle="modal" data-target="#reassign_assignee_modal">Reassign</a></td>
        <?php } else{ ?>
        <td><?php echo $ticket_data->assigned_staff; ?></td>
        <?php }?>
        </tr>
    
        <tr>
            <td style="width:150px;"><strong>Contributors</strong></td>
            <td>
            <?php if (empty($ticket_data->secondary_staffs)){ ?>
                <?php if ($ticket_data->ticket_status == 'Open' || $ticket_data->ticket_status == 'In Progress' || $ticket_data->ticket_status == 'Reopen'){ ?>
                    <a href=""  id="add_secondary_assignee" class="btn btn-primary" data-toggle="modal" data-target="#secondary_staff_uploader" onclick="display_staff(<?php echo $ticket_data->ticket_id?>)">Add</a></td>
                <?php }
                else{ ?>
                    <?php echo '-'?></td>
                <?php } 
            }else{  
            $k = 1; ?>
            <table class="table table-bordered">
                <thead>
                <th> Staff</th>
                <th> Action</th>
                </thead>
                <tbody>
                <?php foreach($ticket_data->secondary_staffs as $key){ ?>
                    <tr>
                        <?php $staff_id = $key->staff_id; ?>
                        <td><?php echo $key->staff_name ?></td>
                        <?php if ($ticket_data->ticket_status == 'Open' || $ticket_data->ticket_status == 'In Progress' || $ticket_data->ticket_status == 'Reopen'){ ?>
                        <td><button id="delete_$staff_id" onclick="remove_contributor(<?php echo $ticket_data->ticket_id ?>, <?php echo $staff_id ?>)" class="btn btn-danger">Remove</button></td>
                        <?php }else{ ?>
                        <td><div class="d-inline-block" tabindex="0" data-toggle="tooltip" data-placement="bottom" title="Button is disabled as the ticket is <?php echo $ticket_data->ticket_status ?>"><button id="delete" class="btn btn-danger" disabled>Remove</button></div></td>`;
                        <?php } ?>
                    </tr>
                    <?php $k++; ?>
                <?php }
                ?>
                </tbody>
            </table>
            <?php if ($ticket_data->ticket_status == 'Open' || $ticket_data->ticket_status == 'In Progress' || $ticket_data->ticket_status == 'Reopen'){ ?>
                <?php if ($ticket_data->logged_in_staff == 'No'){ ?>
                <a href=""  id="add_secondary_assignee" class="btn btn-primary" data-toggle="modal" data-target="#secondary_staff_uploader" onclick="display_staff(<?php echo $ticket_data->ticket_id?>)">Add</a></td>
                <?php }
            }
            } ?>
        </tr>

        <?php $document_path = $ticket_data->doc; ?>
        <tr>
        <?php if(count($document_path) == 0){ ?>
        <td style="width:150px;" rowspan="<?php count($document_path)?>" style="vertical-align:middle;"><strong>Attached Documents</strong></td>
        <td>No documents attached</td>
        <?php }else{ ?>
        <td rowspan="<?php count($document_path)?>" style="vertical-align:middle;"><strong>Attached Documents</strong></td><td>
        <?php foreach($document_path as $path) { ?>
            <a href="<?php echo $path ?>" target="_blank" class="btn btn-secondary" id="essay"> View <i class="fa fa-eye"></i></a><br><br>
        <?php } 
        } ?>
        </td></tr> 
        </table>
    </div>

    <div>
        <a href="<?php echo site_url("internal_ticketing/comments_mobile/$ticket_data->ticket_id")?>"><span id="commentsButton" class="label label-default label-form btn btn-info">Comments</span></a>
        <a href="<?php echo site_url("internal_ticketing/resolve_mobile/$ticket_data->ticket_id")?>" id="resolve_form"><span id="resolveForm" class="label label-default label-form btn btn-info">Resolve</span></a> 
        <?php if($this->settings->getSetting('enable_close_option_in_internal_ticketing')){ ?>
            <a href="<?php echo site_url("internal_ticketing/close_reopen_mobile/$ticket_data->ticket_id")?>"><span id="closeButton" class="label label-default label-form btn btn-info">Close/Reopen</span></a> 
        <?php } else{ ?>
            <a href="<?php echo site_url("internal_ticketing/close_reopen_mobile/$ticket_data->ticket_id")?>"><span id="closeButton" class="label label-default label-form btn btn-info">Reopen</span></a> 
        <?php } ?>
        <a href="<?php echo site_url("internal_ticketing/history_mobile/$ticket_data->ticket_id")?>"><span id="historyButton" class="label label-default label-form btn btn-info">History</span></a> 
    </div>
</div>

<div class="modal fade" id="view_priority_details_modal" tabindex="-1" role="dialog" style="width:100%;margin:auto;top:0%" data-backdrop="static" aria-labelledby="priority_modal" aria-hidden="true">
    <div class="modal-content modal-dialog" style="border-radius: 8px;">
      <div class="modal-header" style="border-bottom: 2px solid #ccc;">
      <h4 class="modal-title" id="modalHeader">Priority Details</h4>
      <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;</button>
    </div>

    <div class="modal-body">
      <table class="table table-bordered">
      <tbody>
        <tr>
          <td style="width:150px;">Name</td>
          <td id="priority_name_modal"></td>
        </tr>
        <tr>
          <td style="width:150px;">Description</td>
          <td id="priority_description"></td>
        </tr>
        <tr>
          <td style="width:150px;">First Escalation Time</td>
          <td id="first_escalation_time"></td>
        </tr>
        <tr>
          <td style="width:150px;">Second escalation time</td>
          <td id="second_escalation_time"></td>
        </tr>
        <tr>
          <td style="width:150px;">Third escalation time</td>
          <td id="third_escalation_time"></td>
        </tr>
      </tbody>

                      
      </table>
    </div>
    <div class="modal-footer">
      <button id="close_priority_modal" onclick="closeModal()" class="btn btn-danger" type="button">Close</button>
    </div>
  </div>
</div>

<div class="modal fade" id="reassign_assignee_modal" tabindex="-1" role="dialog" style="width:100%;margin:auto;top:0%" data-backdrop="static" aria-labelledby="reassign-label" aria-hidden="true">
    <div class="modal-content modal-dialog" style="border-radius: 8px;">
      <div class="modal-header" style="border-bottom: 2px solid #ccc;">
      <h4 class="modal-title" id="modalHeader">Reassign Staff</h4>
      <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;</button>
    </div>

    <div class="modal-body">
      <form id="reassign_staff_form" data-parsley-validate="">
        <input type="hidden" name="reassigned_by" id="reassigned_by" value="<?php echo $this->authorization->getAvatarStakeHolderId() ?>">
        <div class="form-group">
          <label class="col-md-3 col-xs-12 control-label" for="priority">Staff <font color="red">*</font></label>
            <select class="form-control select2" name="selected_staff" id="selected_staff" required>
                <option value="">Select Staff</option>
                <?php 
                foreach($staff_data as $dl => $cl){
                    if ($cl->staff_id == $ticket_data->assigned_to){ ?>
                        <option disabled="" value=<?php echo $cl->staff_id; ?>><?php echo $cl->staff_name; ?></option>
                    <?php } 
                    else{ ?>
                        <option  value=<?php echo $cl->staff_id; ?>><?php echo $cl->staff_name; ?></option>
                    <?php }
                }
                ?>
            </select>
        </div>
      </form>
    </div> 
    <div class="modal-footer">
      <button id="submit_reassigned_staff" class="btn btn-primary" type="button" onclick="reassignStaff(<?php echo $ticket_data->ticket_id?>)">Submit</button>
    </div>
  </div>
</div>

<div class="modal fade" id="reassign_issue_type_modal" tabindex="-1" role="dialog" style="width:100%;margin:auto;top:0%" data-backdrop="static" aria-labelledby="reassign-label" aria-hidden="true">
    <div class="modal-content modal-dialog" style="border-radius: 8px;">
      <div class="modal-header" style="border-bottom: 2px solid #ccc;">
      <h4 class="modal-title" id="modalHeader">Reassign Issue Type</h4>
      <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;</button>
    </div>

    <div class="modal-body">
      <form id="reassign_issue_type_form" data-parsley-validate="">
        <input type="hidden" name="reassigned_issuetype_by" id="reassigned_issuetype_by" value="<?php echo $this->authorization->getAvatarStakeHolderId() ?>">
        <div class="form-group">
          <label class="col-md-3 col-xs-12 control-label">Issue Type<font color="red">*</font></label>
          <div class="form-group">
            <select class="form-control select2" name="selected_issue_type" id="selected_issue_type" required>
                <option value="">Select Issue Type</option>
                <?php 
                foreach($ticket_data->issue_type_data as $val){
                    if ($val->issue_type_id == $ticket_data->issue_type){ ?>
                        <option disabled="" value=<?php echo $val->issue_type_id; ?>><?php echo $val->issue_type_name; ?></option>
                    <?php } 
                    else{ ?>
                        <option  value=<?php echo $val->issue_type_id; ?>><?php echo $val->issue_type_name; ?></option>
                    <?php }
                }
                ?>
            </select>
          </div>
        </div>
        <br>
        <br>
      </form>
    </div> 
    <div class="modal-footer">
      <button id="submit_reassigned_issue_type" class="btn btn-primary" type="button" onclick="reassign_issue_type(<?php echo $ticket_data->ticket_id?>)">Submit</button>
    </div>
  </div>
</div>

<div class="modal fade" id="secondary_staff_uploader" tabindex="-1" role="dialog" style="width:100%;margin:auto;top:0%" data-backdrop="static" aria-labelledby="secondary_staff_uploader_label" aria-hidden="true">
    <div class="modal-content modal-dialog" style="border-radius: 8px;">
      <div class="modal-header" style="border-bottom: 2px solid #ccc;">
      <h4 class="modal-title" id="modalHeader">Add Contributors</h4>
      <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;</button>
    </div>

    <div class="modal-body">
      <form id="staff_form" data-parsley-validate="">
        <input type="hidden" name="added_by" id="added_by" value="<?php echo $this->authorization->getAvatarStakeHolderId() ?>">
        <div class="col-md-12"  style="margin-top: 10px;">
          <div class="form-group">
            <label for="secondary_assignee" class="col-md-3"> Staff Name<font color="red">*</font></label>
            <div class="col-md-8" id="secondary_assignee_div">
              
            </div>
          </div>
        </div>
      </form>
    </div> 
    <div class="modal-footer">
      <button id="submit_secondary_staff" class="btn btn-primary" type="button" onclick="submit_secondary_staff(<?php echo $ticket_data->ticket_id ?>)">Submit</button>
    </div>
  </div>
</div>

<?php $this->load->view('internal_ticketing/_manage_tickets_script.php'); ?>


<style type="text/css">
.loaderclass {
  border: 8px solid #eee;
  border-top: 8px solid #7193be;
  border-radius: 50%;
  width: 48px;
  height: 48px;
  position: fixed;
  z-index: 1;
  animation: spin 2s linear infinite;
  margin-top: 35%;
  margin-left: 40%;
  position: absolute;
  z-index: 99999;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.active{
  background: #6893ca;
}

.disabled{
  background: #41474e57;
}

.danger{
  background: #FF0000;
}
.list-group-item{
    margin-bottom: 1px;
  }
  .confirmBootboxClass{
    width:30%;
    margin:auto;
  }

  .widthadjust{
		width:600px;
		margin:auto;
	}
  input[type="checkbox"] {
  width: 25px; /* Set the width */
  height: 25px; /* Set the height */
  /* Other styling properties */
  }
  .new_circleShape_res {
        margin: -4px 0 0 0;
        padding: 8px 6px;
        float: right;
        border-radius: 50% !important;
        color: white !important;
        font-size: 17px;
        height: 2.8rem !important;
        width: 2.8rem !important;
        text-align: center;
        vertical-align: middle;
        border: none !important;
        box-shadow: 0px 3px 7px #ccc;
        line-height: 1.7rem !important;
    }
  .label-default,.label-success,.label-danger {
    cursor: pointer;
  }
  .editable, .select-editable {
    cursor: pointer;
    position: relative;
    margin-left: 5px;
  }
  .editable:hover, .select-editable:hover {
    font-weight: 700;
  }
  .editable::before, .select-editable::before {
    font-family: "FontAwesome";
    content: "\f040";
    display: inline-block;
    padding-right: 5px;
    vertical-align: middle;
    font-weight: 900;
    position: absolute;
    right: 5px;
  }

  .select-editable-board, .select-editable-transport,.select-editable-gender, .select-editable-tcertificate, .select-editable-bordingtype, .select-editable-gottoknow, .select-editable-previousacademic, .select-editable-combination, .select-editable-additionalcoaching, .select-editable-currentcountry {
    cursor: pointer;
    position: relative;
    margin-left: 5px;
  }
  .select-editable-currentcountry:hover, .select-editable-currentcountry:hover {
    font-weight: 700;
  }
  .select-editable-additionalcoaching:hover, .select-editable-additionalcoaching:hover {
    font-weight: 700;
  }
  .select-editable-combination:hover, .select-editable-combination:hover {
    font-weight: 700;
  }
  .select-editable-previousacademic:hover, .select-editable-previousacademic:hover {
    font-weight: 700;
  }
  .select-editable-gottoknow:hover, .select-editable-gottoknow:hover {
    font-weight: 700;
  }
  .select-editable-board:hover, .select-editable-board:hover ,.select-editable-transport:hover{
    font-weight: 700;
  }

  .select-editable-gender:hover, .select-editable-gender:hover {
    font-weight: 700;
  }
  .select-editable-tcertificate:hover, .select-editable-tcertificate:hover {
    font-weight: 700;
  }
  .select-editable-bordingtype:hover, .select-editable-bordingtype:hover {
    font-weight: 700;
  }


  .select-editable-currentcountry::before, .select-editable-currentcountry::before {
    font-family: "FontAwesome";
    content: "\f040";
    display: inline-block;
    padding-right: 5px;
    vertical-align: middle;
    font-weight: 900;
    position: absolute;
    right: 5px;
  }
  .select-editable-additionalcoaching::before, .select-editable-additionalcoaching::before {
    font-family: "FontAwesome";
    content: "\f040";
    display: inline-block;
    padding-right: 5px;
    vertical-align: middle;
    font-weight: 900;
    position: absolute;
    right: 5px;
  }
  .select-editable-combination::before, .select-editable-combination::before {
    font-family: "FontAwesome";
    content: "\f040";
    display: inline-block;
    padding-right: 5px;
    vertical-align: middle;
    font-weight: 900;
    position: absolute;
    right: 5px;
  }
  .select-editable-previousacademic::before, .select-editable-previousacademic::before {
    font-family: "FontAwesome";
    content: "\f040";
    display: inline-block;
    padding-right: 5px;
    vertical-align: middle;
    font-weight: 900;
    position: absolute;
    right: 5px;
  }
  .select-editable-gottoknow::before, .select-editable-gottoknow::before {
    font-family: "FontAwesome";
    content: "\f040";
    display: inline-block;
    padding-right: 5px;
    vertical-align: middle;
    font-weight: 900;
    position: absolute;
    right: 5px;
  }
  .select-editable-bordingtype::before, .select-editable-bordingtype::before {
    font-family: "FontAwesome";
    content: "\f040";
    display: inline-block;
    padding-right: 5px;
    vertical-align: middle;
    font-weight: 900;
    position: absolute;
    right: 5px;
  }
  .select-editable-board::before, .select-editable-board::before,.select-editable-transport::before {
    font-family: "FontAwesome";
    content: "\f040";
    display: inline-block;
    padding-right: 5px;
    vertical-align: middle;
    font-weight: 900;
    position: absolute;
    right: 5px;
  }
  .select-editable-gender::before, .select-editable-gender::before {
    font-family: "FontAwesome";
    content: "\f040";
    display: inline-block;
    padding-right: 5px;
    vertical-align: middle;
    font-weight: 900;
    position: absolute;
    right: 5px;
  }
  .select-editable-gender::before, .select-editable-tcertificate::before {
    font-family: "FontAwesome";
    content: "\f040";
    display: inline-block;
    padding-right: 5px;
    vertical-align: middle;
    font-weight: 900;
    position: absolute;
    right: 5px;
  }
  .list-group-item.active{
    background-color: #ebf3f9;
    border-color: #ebf3f9;
    color: #737373;
  }
  .list-group-item.active, .list-group-item.active:hover, .list-group-item.active:focus{
    background: #ebf3f9;
    color: #737373;
  }
  .list-group-item{
    border:none;
  }

   .new_circleShape_res {
  display:inline-block;
  height:12px;
  width:12px;
  margin:auto;
  text-align:left;
  }

</style>

<script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/summernote/summernote.js"></script>
<script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/jeditor/editable.js"></script>
<script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/jeditor/editable_date.js"></script>
<script type="text/javascript">
</script>


<style>
  .medium{
    width: 45%;
    margin: auto;
  }
</style>

<script>
</script>