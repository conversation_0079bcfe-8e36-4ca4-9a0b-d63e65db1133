<div class="card panel_new_style">
    <div class="card-header panel_heading_new_style_padding" style="padding-top: 10px;">
        <h3 class="card-title panel_title_new_style">
          <a class="back_anchor" href='<?php echo site_url("internal_ticketing"); ?>'>
            <span class="fa fa-arrow-left"></span>
          </a>
            <strong>My Tickets</strong>
        </h3>
    </div>
    <div class="card-body">
      <div id="loader" class="loaderclass" style="display:none;"></div>
      <div class="list-group-ticket"></div>
    </div>
</div>

<style type="text/css">
.loaderclass {
  border: 8px solid #eee;
  border-top: 8px solid #7193be;
  border-radius: 50%;
  width: 48px;
  height: 48px;
  position: fixed;
  z-index: 1;
  animation: spin 2s linear infinite;
  margin-top: 35%;
  margin-left: 40%;
  position: absolute;
  z-index: 99999;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.active{
  background: #6893ca;
}

.disabled{
  background: #41474e57;
}

.danger{
  background: #FF0000;
}
</style>

<script>
  $(document).ready(function(){
      get_ticket_data();
  });

  function get_ticket_data() {
    $('#loader').show();

    $.ajax({
      url: '<?php echo site_url('internal_ticketing/get_ticket_data'); ?>',
      data: {
        'issue_type': '',
        'priority': '',
        'department': '',
        'status': '',
        'reported_by_me': 'on',
        'assigned_to_me': '',
        'filter_type': 'reported_by_me'
      },
      type: "post",
      success: function (data) {
        $('#loader').hide();
        var data = JSON.parse(data);
        if (data == '' || data.length == 0) {
          $('.list-group-ticket').html('<div style="text-align:center; padding:50px;"><h5>No tickets found</h5><p>You haven\'t created any tickets yet.</p></div>');
          return false;
        } else {
          $('.list-group-ticket').html(construct_ticket_data(data));
        }
      },
      error: function (err) {
        $('#loader').hide();
        console.log(err);
      }
    });
  }

  function construct_ticket_data(tickets){
    var ticketArr = [];
    for(var e = 0; e < tickets.length; e++){
      ticketArr.push(tickets[e]);  
    }
    return construct_ticket_data1(ticketArr);
  }

  function construct_ticket_data1(ticket_data) {
    var output = '';

    for(var i = 0; i < ticket_data.length; i++){
      var ticket = ticket_data[i];

      // Status badge styling - different icons for different statuses
      var status_badge = '';
      var status_icon = '';
      if (ticket.ticket_status == 'Open' || ticket.ticket_status == 'Reopen') {
        status_badge = 'background-color:#c5bebe; color: white;';
        status_icon = '📂'; // Open folder icon
      } else if (ticket.ticket_status == 'In Progress') {
        status_badge = 'background-color:#c5bebe; color: white;';
        status_icon = '⏳'; // Hourglass icon
      } else if (ticket.ticket_status == 'Resolved') {
        status_badge = 'background-color: #c5bebe; color: white;';
        status_icon = '✅'; // Check mark icon
      } else if (ticket.ticket_status == 'Closed') {
        status_badge = 'background-color: #c5bebe; color: white;';
        status_icon = '🔒'; // Lock icon
      } else {
        status_badge = 'background-color: #c5bebe; color: white;';
        status_icon = '❓'; // Question mark icon
      }

      // Priority badge styling - matching details page colors
      var priority_badge = '';
      if (ticket.priority_name == 'High') {
        priority_badge = 'background-color: #D93E39; color: white;';
      } else if (ticket.priority_name == 'Medium') {
        priority_badge = 'background-color: #FFA500; color: white;';
      } else {
        priority_badge = 'background-color: #28a745; color: white;';
      }

      // Card container - matching your image style
      output += '<div style="background: #E8EAF6; border-radius: 8px; padding: 16px; margin-bottom: 12px; cursor: pointer;" onclick="window.location.href=\'<?php echo site_url('internal_ticketing/view_ticket_mobile/'); ?>\' + \'' + ticket.ticketid + '\'">';

      // Status and Priority badges row
      output += '<div style="display: flex; gap: 8px; margin-bottom: 12px;">';
      output += '<span style="' + status_badge + ' padding: 4px 12px; border-radius: 4px; font-size: 12px; font-weight: 500; display: flex; align-items: center; gap: 4px;">';
      output += '<span style="font-size: 10px;">' + status_icon + '</span> ' + ticket.ticket_status;
      output += '</span>';
      output += '<span style="' + priority_badge + ' padding: 4px 12px; border-radius: 4px; font-size: 12px; font-weight: 500; display: flex; align-items: center; gap: 4px;">';
      output += '<span style="font-size: 10px;">●</span> ' + ticket.priority_name;
      output += '</span>';
      output += '</div>';

      // Ticket ID
      output += '<h3 style="margin: 0 0 8px 0; font-size: 20px; font-weight: 600; color: #333;">' + ticket.ticket_item_id + '</h3>';

      // Description/Title
      var description = ticket.title || ticket.description || 'No description available';
      output += '<p style="margin: 0 0 16px 0; color: #666; font-size: 14px; line-height: 1.4;">' + description.substring(0, 100) + (description.length > 100 ? '...' : '') + '</p>';

      // Separator line
      output += '<hr style="border: none; border-top: 1px solid #BDBDBD; margin: 16px 0;">';

      // Bottom section with date only
      output += '<div style="display: flex; justify-content: flex-end;">';

      // Date with calendar icon
      output += '<div style="display: flex; align-items: center; gap: 4px;">';
      output += '<span style="font-size: 12px; color: #9E9E9E;">📅</span>';
      output += '<span style="font-size: 12px; color: #666;">' + ticket.created_on + '</span>';
      output += '</div>';

      output += '</div>';
      output += '</div>';
    }

    return output;
  }

  function getStatusBadgeClass(status) {
    switch(status) {
      case 'Open':
        return 'badge-primary';
      case 'In Progress':
        return 'badge-warning';
      case 'Resolved':
        return 'badge-success';
      case 'Closed':
        return 'badge-secondary';
      case 'Reopen':
        return 'badge-info';
      default:
        return 'badge-light';
    }
  }
</script>
