<?php

class Event_model extends CI_Model {
  private $yearId;
  private $current_branch;
  public function __construct() {
    parent::__construct();
    $this->yearId =  $this->acad_year->getAcadYearId();
    $this->current_branch = $this->authorization->getCurrentBranch();
  }
  
  public function Kolkata_datetime(){
    $timezone = new DateTimeZone("Asia/Kolkata" );
    $date = new DateTime();
    $date->setTimezone($timezone );
    $dtobj = $date->format('Y-m-d H:i:s');
    return $dtobj;
  }

  public function getLastInserted(){
    $this->db->select('id');
    $this->db->from('event_master_v2');
    $this->db->order_by('id', 'DESC');
    $res = $this->db->get()->row();
      if (!empty($res)) {
        $creationId1= $res->id+1;
        $creationId = sprintf('%06d',$creationId1);
      }else{
        $creationId2=1;
        $creationId = sprintf('%06d',$creationId2);
      }
    return $creationId;
  }

  public function insert_eventDetails($attachmentPath){
    $input = $this->input->post();
    $this->db->trans_start();
    $reg_start_date = $input['start_date_of_registration'].' '.$input['start_time_of_registration'];
    $reg_end_date = $input['end_date_of_registration'].' '.$input['end_time_of_registration'];
    $event_data= array(
      'event_name' => $input['event_name'],
      'no_of_person_allowed_per_registration' => $input['no_of_person_allowed'],
      'max_number_of_allowed' => $input['max_number_of_allowed'],
      'organizer' => $input['organizer'],
      'event_venue' => $input['event_venue'],
      'event_venue_address' => (isset($input['event_venue_address']))? $input['event_venue_address'] : NULL,
      'registration_amount' => $input['registration_amount'],
      'safety_deposit_amount' => $input['safety_deposit_amount'],
      'who_can_register' => $input['who_can_register'],
      'terms_conditions'=>$input['terms_conditions'],
      'created_by' => $this->authorization->getAvatarId(),
      'created_at' => $this->Kolkata_datetime(),
      'acad_year_id' => $this->yearId,
      'reg_start_date' => date('Y-m-d H:i',strtotime($reg_start_date)),
      'reg_end_date' => date('Y-m-d H:i',strtotime($reg_end_date)),
      'event_description' => $input['event_description'],
      'status' => 'UNPUBLISH',
      'class_sections_id'=> json_encode($input['section_name']),
      'event_file'=>($attachmentPath['file_name'] == '') ? null : $attachmentPath['file_name'],
      'is_sub_event'=> (isset($input['is_sub_event']))? $input['is_sub_event'] : NULL,
      'sub_event_select_number'=> (isset($input['sub_event_select_number']))? $input['sub_event_select_number'] : NULL,
      'sub_event_header'=> (isset($input['sub_event_header']))? $input['sub_event_header'] : NULL,
      'is_sub_event_mandatory'=> (isset($input['is_sub_event_mandatory']))? $input['is_sub_event_mandatory'] : NULL,
    );

    $this->db->insert('event_master_v2',$event_data);
    $event_id = $this->db->insert_id();
   
    $event_time = [];
    foreach ($input['event_start_date'] as $key => $start_date) {
      if (!empty($start_date)) {
        $event_time[] = array(
          'event_date' => date('Y-m-d',strtotime($start_date)),
          'start_time' => $input['event_start_time'][$key],
          'end_time' => $input['event_end_time'][$key],
          'description' => $input['description'][$key],
          'event_id' =>  $event_id
        );
      }
    }
    $this->db->insert_batch('event_v2_time_details',$event_time);
    $this->db->trans_complete();
    return $this->db->trans_status();

  }

  public function get_event_details(){
    $eventMaster = $this->db->select('*')
    ->from('event_master_v2')
    ->where('acad_year_id', $this->yearId)
    ->order_by('id','desc')
    ->get()->result();

    $eventTime = $this->db->select("event_id, date_format(event_date,'%d-%b-%Y') as eventdate, time_format(start_time,'%h:%i %p') as starttime, time_format(end_time,'%h:%i %p') as endtime")
    ->from('event_v2_time_details')
    ->get()->result();
    $eventDates = [];
    foreach ($eventTime as $key => $val) {
      $eventDates[$val->event_id][] = $val;
    }

    foreach ($eventMaster as $key => &$val) {
      if (array_key_exists($val->id,$eventDates)) {
        $val->event_date = $eventDates[$val->id];
      }
    }
    return $eventMaster;
    // return $this->db->order_by('id','desc')->get('event_master_v2')->result();
  }

  public function get_event_namebyid($event_id){
    return $this->db->select('*')->from('event_master_v2')->where('id',$event_id)->get()->row();
  }

  public function publish_status_update_event_by_id($stngId,$value){
    $data = array(
        'status' => $value,
    );
    $this->db->where('id',$stngId);
    return $this->db->update('event_master_v2', $data);
  }

  public function event_invented_countSectionWise(){
    $inviteCount = $this->db->select("count(erd.student_id) stdCount, sum(ifnull(erd.no_of_person_entry,'')) as pEntry, erd.event_id")
    ->from('event_registrations erd')
    ->group_by('erd.event_id')
    ->get()->result();
    return $inviteCount;
  }

 
  public function update_event_status($stngId,$value){
    $data = array(
      'status' => $value,
    );
    $this->db->where('id',$stngId);
    return $this->db->update('event_master_v2', $data);
  }

  public function edit_evenit_byId($id){
    $event = $this->db->select('*')
    ->from('event_master_v2')
    ->where('id',$id)
    ->get()->row();

    $eventDate = $this->db->select('*')
    ->from('event_v2_time_details')
    ->where('event_id',$id)
    ->get()->result();

    return array('event' =>$event, 'event_date' =>$eventDate);
  }

  public function get_event_list_for_registration($event_id){
    return $this->db_readonly->select("emv.id,emv.event_name, date_format(reg_start_date,'%d-%m-%Y') as  event_date, date_format(reg_start_date,'H:i') as start_time, date_format(reg_end_date,'H:i') as end_time, status, organizer,no_of_person_allowed_per_registration,registration_amount, safety_deposit_amount, max_number_of_allowed")
    ->from('event_master_v2 emv')
    ->where('emv.id', $event_id)
    // ->join('event_v2_time_details etd','emv.id=etd.event_id')
    ->order_by('reg_start_date')
    ->get()->result();
  }

  public function get_published_events_list(){
    return $this->db->where('status',1)->order_by('reg_start_date')->get('event_master_v2')->result();
  }

  public function get_event_details_by_id($id){
    return $this->db_readonly->select('em.*')
    ->from('event_master_v2 em')
    ->where('em.id',$id)
    ->get()->row();
  }

  public function get_event_student_list($event_id){
    $prefix_student_name = $this->settings->getSetting('prefix_student_name');
    $prefix_order_by = $this->settings->getSetting('prefix_order_by');

    $order_by = 'sa.first_name';
    if ($prefix_order_by == "roll_number") {
      $order_by = 'sy.roll_no';
    }else if($prefix_order_by == "enrollment_number"){
      $order_by = 'sa.enrollment_number';
    }else if($prefix_order_by == "admission_number"){
      $order_by = 'sa.admission_number';
    }else if($prefix_order_by == "alpha_rollnum"){
      $order_by = 'sy.alpha_rollnum';
    }

		if ($prefix_student_name == "roll_number") {
      $std_name = "CONCAT(if(sy.roll_no = 0, 'NA', sy.roll_no), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
    } else if ($prefix_student_name == "enrollment_number") {
      $std_name = "CONCAT(ifnull(sa.enrollment_number, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
    } else if ($prefix_student_name == "admission_number") {
      $std_name = "CONCAT(ifnull(sa.admission_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
    } else if ($prefix_student_name == "alpha_rollnum") {
        $std_name = "CONCAT(ifnull(sy.alpha_rollnum, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
    } else {
      $std_name = "CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
    }
    return $this->db_readonly->select("er.*, sa.id as student_id, $std_name, cs.section_name, cs.class_name, ")
    ->from('event_registrations er')
    ->where('er.event_id',$event_id)
    ->join('student_admission sa','er.student_id=sa.id')
    ->join('student_year sy', 'sa.id=sy.student_admission_id')
    ->join('class_section cs', 'cs.id=sy.class_section_id')
    ->where('sy.acad_year_id', $this->yearId)
    ->order_by('cs.class_name, cs.section_name,'.$order_by)
    ->get()->result();
  }

  public function getSectionStudents_event($sectionId) {
    $display_roll_no_with_student_name = $this->settings->getSetting('display_roll_no_with_student_name');
    if ($display_roll_no_with_student_name == 1) {
      $std_name = "CONCAT(sy.roll_no, ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
    } else {
      $std_name = "CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
    }

    $this->db_readonly->select("sa.id, $std_name");
    $this->db_readonly->from('student_admission sa');
    $this->db_readonly->join('student_year sy', 'sa.id=sy.student_admission_id');
    $this->db_readonly->where('sy.class_section_id', $sectionId);
    $this->db_readonly->where('sa.admission_status', 2);
    $this->db_readonly->where('sy.promotion_status!=', '4');
    $this->db_readonly->where('sy.acad_year_id', $this->yearId);
    $this->db_readonly->order_by('sy.roll_no,sa.first_name');
    return $this->db_readonly->get()->result();
  }

  public function getExistingevent_register($event_id){
    $result =  $this->db->select('student_id')
    ->from('event_registrations')
    ->where('event_id',$event_id)
    ->get()->result();

     $ids = [];
      foreach ($result as $key => $res) {
        $ids[] = $res->student_id;
      }
      return $ids;
  }

  public function insert_register_student_data($event_data){
    return $this->db->insert_batch('event_registrations',$event_data);
  }

  public function remove_student_register_id($regid){
    $this->db->where('id',$regid);
    return $this->db->delete('event_registrations');
  }

  public function get_all_section_list(){
    $this->db_readonly->select('cs.id, section_name, cs.class_name, class_id')
    ->join('class c', 'c.id=cs.class_id')
    ->where("c.acad_year_id",$this->yearId);
    if($this->current_branch) {
        $this->db_readonly->where('c.branch_id',$this->current_branch);
    }
    $this->db_readonly->where('cs.is_placeholder', 0);
    return $this->db_readonly->get('class_section cs')->result();
  }

  public function get_classes_event(){
    $this->db_readonly->select('c.id, c.class_name')
    ->from('class c')
    ->where("c.acad_year_id",$this->yearId);
    if($this->current_branch) {
        $this->db_readonly->where('c.branch_id',$this->current_branch);
    }
    $this->db_readonly->where('c.is_placeholder', 0);
    return $this->db_readonly->get()->result();
  }

  public function getSectionwiseStudent_list($sectionId,$event_id) {

    $prefix_student_name = $this->settings->getSetting('prefix_student_name');
    $prefix_order_by = $this->settings->getSetting('prefix_order_by');

    $order_by = 'sa.first_name';
    if ($prefix_order_by == "roll_number") {
      $order_by = 'sy.roll_no';
    }else if($prefix_order_by == "enrollment_number"){
      $order_by = 'sa.enrollment_number';
    }else if($prefix_order_by == "admission_number"){
      $order_by = 'sa.admission_number';
    }else if($prefix_order_by == "alpha_rollnum"){
      $order_by = 'sy.alpha_rollnum';
    }

		if ($prefix_student_name == "roll_number") {
      $std_name = "CONCAT(if(sy.roll_no = 0, 'NA', sy.roll_no), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
    } else if ($prefix_student_name == "enrollment_number") {
      $std_name = "CONCAT(ifnull(sa.enrollment_number, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
    } else if ($prefix_student_name == "admission_number") {
      $std_name = "CONCAT(ifnull(sa.admission_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
    } else if ($prefix_student_name == "alpha_rollnum") {
        $std_name = "CONCAT(ifnull(sy.alpha_rollnum, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
    } else {
      $std_name = "CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
    }

    $this->db_readonly->select("sa.id, $std_name, if(er.student_id IS NOT NULL, 1, 0) as disabled");
    $this->db_readonly->from('student_admission sa');
    $this->db_readonly->join('student_year sy', 'sa.id=sy.student_admission_id');
    $this->db_readonly->join('event_registrations er', 'er.student_id=sa.id and event_id='.$event_id,'left');
    $this->db_readonly->where('sy.class_section_id', $sectionId);
    $this->db_readonly->where('sa.admission_status', 2);
    $this->db_readonly->where('sy.promotion_status!=', '4');
    $this->db_readonly->where('sy.acad_year_id', $this->yearId);
    $this->db_readonly->order_by($order_by);
    return $this->db_readonly->get()->result();
  }

  public function update_eventDetails($id, $file){

    $input = $this->input->post();
    $this->db->trans_start();
    $reg_start_date = $input['start_date_of_registration'].' '.$input['start_time_of_registration'];
    $reg_end_date = $input['end_date_of_registration'].' '.$input['end_time_of_registration'];

    $event_data= array(
      'event_name' => $input['event_name'],
      'no_of_person_allowed_per_registration' => $input['no_of_person_allowed'],
      'max_number_of_allowed' => $input['max_number_of_allowed'],
      'organizer' => $input['organizer'],
      'event_venue' => $input['event_venue'],
      'event_venue_address' => (isset($input['event_venue_address']))? $input['event_venue_address'] : NULL,
      'registration_amount' => $input['registration_amount'],
      'safety_deposit_amount' => $input['safety_deposit_amount'],
      'who_can_register' => $input['who_can_register'],
      'terms_conditions'=>$input['terms_conditions'],
      'created_by' => $this->authorization->getAvatarId(),
      'created_at' => $this->Kolkata_datetime(),
      'acad_year_id' => $this->yearId,
      // 'status' => 'UNPUBLISH',
      'reg_start_date' => date('Y-m-d H:i',strtotime($reg_start_date)),
      'reg_end_date' => date('Y-m-d H:i',strtotime($reg_end_date)),
      'event_description' => $input['event_description'],
      'class_sections_id'=> json_encode($input['section_name']),
      'event_file'=>($file['file_name'] == '') ? null : $file['file_name'],
      'is_sub_event'=> (isset($input['is_sub_event']))? $input['is_sub_event'] : NULL,
      'sub_event_select_number'=> (isset($input['sub_event_select_number']))? $input['sub_event_select_number'] : NULL,
      'sub_event_header'=> (isset($input['sub_event_header']))? $input['sub_event_header'] : NULL,
      'is_sub_event_mandatory'=> (isset($input['is_sub_event_mandatory']))? $input['is_sub_event_mandatory'] : NULL,

    );

    $this->db->where('id',$id);
    $this->db->update('event_master_v2',$event_data);

    $event_time = [];
    foreach ($input['event_start_date'] as $key => $start_date) {
      if (!empty($start_date)) {
        $event_time[] = array(
          'event_date' => date('Y-m-d',strtotime($start_date)),
          'start_time' => $input['event_start_time'][$key],
          'end_time' => $input['event_end_time'][$key],
          'description' => $input['description'][$key],
          'event_id' =>  $id
        );
        $this->db->where('event_id',$id);
        $this->db->delete('event_v2_time_details');
        
        $this->db->insert_batch('event_v2_time_details',$event_time);
      }
    }
    $this->db->trans_complete();
    return $this->db->trans_status();

  }


  public function get_classSection(){
    $this->db->select("c.id as clsId, cs.id as sectionId, concat(ifnull(c.class_name,''),'',ifnull(cs.section_name,'')) as clsSectionName");
    $this->db->from('class c');
    $this->db->where('acad_year_id',$this->yearId);
    $this->db->join('class_section cs',"cs.class_id=c.id and cs.is_placeholder!=1");
    return $this->db->get()->result();
  }

  public function register_stdsbyClassId($evenId, $input){
    $stdIds = $this->db->select('sa.id as stdId, sy.class_section_id')
    ->from('student_admission sa')
    ->where('sa.admission_status',2)
    ->join('student_year sy','sa.id=sy.student_admission_id')
    ->where_in('sy.class_section_id',$input['section_ids'])
    ->get()->result();
    // echo "<pre>"; print_r($stdIds); die();
    if (!empty($stdIds)) {
      $sName = $this->settings->getSetting('school_short_name');
      $eventData =[];
      foreach ($stdIds as $key => $val) {
        $len = strlen((string)$val->stdId);
        $digits = 'E';
        for ($i = 6 - $len;$i > 0; $i--) { 
          $digits .= '0';
        }
        $s_short_name = $sName;
        $digits .= $evenId.$val->stdId;
        $qrCode = strtoupper($s_short_name).$digits;
        $eventData[] = array(
          'event_id' => $evenId,
          'student_id' => $val->stdId,
          'qr_code'=>$qrCode,
          'section_id'=>$val->class_section_id,
          'created_by'=>$this->authorization->getAvatarId()
        );
      }
      return $this->db->insert_batch('event_registrations',$eventData);
    }else{
      return 0;
    }
  }

  public function get_regStds_details($evnId){

    $sqlSection = "SELECT concat(ifnull(class_name,''),ifnull(section_name,'')) as sectionName, cs.id as sectionId FROM class_section cs where cs.id in (select distinct(section_id) from event_registrations where event_id=$evnId)";
    $regStds = $this->db->query($sqlSection)->result();
    $sections = [];
    $sectionsName = [];
    foreach ($regStds as $key => $value) {
      array_push($sections, $value->sectionId);
      array_push($sectionsName, $value);
    }
    return array('regSectionName'=>$sectionsName,'regSectionIds'=>$sections);
  }

  public function delete_classSectionbyId($evnId, $sectionid){
    $this->db->where('event_id',$evnId);
    $this->db->where('section_id',$sectionid);
    return $this->db->delete('event_registrations');    
  }

  public function get_event_detailsbyId($eventId){
    $event = $this->db->select('emv.*')
    ->from('event_master_v2 emv')
    ->where('emv.id',$eventId)
    ->get()->row();

    $selectedClss = $this->db->select("concat(ifnull(cs.class_name,''),'',ifnull(cs.section_name,'')) as sectionName, cs.id as sectionIds")
    ->from('event_registrations erd')
    ->where('erd.event_id',$eventId)
    ->join('class_section cs','erd.section_id=cs.id')
    ->group_by('erd.section_id')
    ->get()->result();
    $event->sections = '';
    $event->sectionsIds= '';
    foreach ($selectedClss as $key => $val) {
      if (!empty($event->sections))
        $event->sections .=',';
        $event->sections .= $val->sectionName;      
       if (!empty($event->sectionsIds))
        $event->sectionsIds .='_';
        $event->sectionsIds .= $val->sectionIds;     
    }
    return $event;

  }

  public function match_qr_code_content($qrNumber, $eventId){
    $result = $this->db->select('erd.id')
    ->from('event_registrations erd')
    ->join('event_master_v2 emv',"emv.id=erd.event_id and status=1 and emv.id=$eventId")
    ->where('erd.qr_code',$qrNumber)
    ->get()->row();
    if (!empty($result)) {
      return 1;
    }else{
      return 0;
    }
  }

  public function get_student_event_details($qrCode){
    return $this->db->select("emv.*, erd.id as erdId, erd.no_of_person_entry, concat(ifnull(cs.class_name,''),'',ifnull(cs.section_name,'')) as sectionName,concat(ifnull(sa.first_name,''),'',ifnull(sa.last_name,'')) as stdName,concat(ifnull(p.first_name,''),'',ifnull(p.last_name,'')) as fatherName")
    ->from('event_master_v2 emv')
    ->join('event_registrations erd',"emv.id=erd.event_id and erd.qr_code='$qrCode'")
    ->join('student_admission sa','erd.student_id=sa.id')
    ->join('student_year sy','sa.id=sy.student_admission_id')
    ->join('class_section cs',"sy.class_section_id=cs.id and cs.is_placeholder!=1")
    ->join('student_relation sr',"sr.std_id=sa.id and where relation_type='Father'")
    ->join('parent p','p.id=sr.relation_id')
    ->get()->row();
  }

  public function update_number_of_person_enter($erdId){
    $personCount = $this->input->post('person_count');
    $follow_up_by = $this->authorization->getAvatarId();
    return $this->db->query("UPDATE event_registrations SET no_of_person_entry = no_of_person_entry + $personCount, follow_up_by = $follow_up_by  WHERE id=$erdId");
  }

  public function get_event_name_list(){
    return $this->db->select('id, event_name')
      ->from('event_master_v2')
      ->order_by('id','desc')
      ->get()->result();
  }

  public function get_register_student_data($student_id){
    return $this->db_readonly->select("er.id,er. concat(ifnull(cs.class_name,''),'',ifnull(cs.section_name,'')) as sectionName,concat(ifnull(sa.first_name,''),'',ifnull(sa.last_name,'')) as stdName, swt.amount as registration_amount, sdaw.amount as safety_deposit_amount")
    ->from('event_registrations er')
    ->where_in('er.student_id',$student_id)
    ->join('student_admission sa','er.student_id=sa.id')
    ->join('student_year sy','sa.id=sy.student_admission_id')
    ->join('class_section cs',"sy.class_section_id=cs.id and cs.is_placeholder!=1")
    ->join('student_wallet_transactions swt','er.registration_wallet_tx_id=swt.id')
    ->join('safety_depost_amount_wallet_tx_id sdaw','er.safety_deposit_amount_return_tx_id=sdaw.id')
    ->get()->result();
  }

  public function get_registered_student_listby_event_id($event_id){

    $prefix_student_name = $this->settings->getSetting('prefix_student_name');
		if ($prefix_student_name == "roll_number") {
      $std_name = "CONCAT(if(sy.roll_no = 0, 'NA', sy.roll_no), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
    } else if ($prefix_student_name == "enrollment_number") {
      $std_name = "CONCAT(ifnull(sa.enrollment_number, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
    } else if ($prefix_student_name == "admission_number") {
      $std_name = "CONCAT(ifnull(sa.admission_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
    } else if ($prefix_student_name == "alpha_rollnum") {
        $std_name = "CONCAT(ifnull(sy.alpha_rollnum, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
    } else {
      $std_name = "CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
    }  

    $result =  $this->db_readonly->select("er.id, concat(ifnull(cs.class_name,''),'',ifnull(cs.section_name,'')) as sectionName,$std_name, swt.amount as registration_amount, sdaw.amount as safety_deposit_amount, registered_on, (case when er.sub_event_id = 'null' then 0 else  ifnull(er.sub_event_id,0) end) as sub_event_id")
    ->from('event_registrations er')
    ->where_in('er.event_id',$event_id)
    ->join('student_admission sa','er.student_id=sa.id')
    ->join('student_year sy',"sa.id=sy.student_admission_id and sy.acad_year_id=$this->yearId")
    ->join('class_section cs',"sy.class_section_id=cs.id and cs.is_placeholder!=1")
    ->join('student_wallet_transactions swt','er.registration_wallet_tx_id=swt.id','left')
    ->join('student_wallet_transactions sdaw','er.safety_depost_amount_wallet_tx_id=sdaw.id','left')
    ->order_by('er.registered_on','asc')
    ->get()->result();
    foreach ($result as $key => $val) {
      $val->registered_on = local_time($val->registered_on, 'd-m-Y h:i A');
      if(!empty($val->sub_event_id)){
        $res = json_decode($val->sub_event_id);
        foreach ($res as $key => $va) {
          $val->subEventName[] = $this->db_readonly->select('sub_event_name, date_format(sub_event_date, "%d-%b-%Y") as sub_event_date, date_format(start_time_of_registration, "%H:%i") as start_time_of_registration, date_format(end_time_of_registration, "%H:%i") as end_time_of_registration')
           ->from('eventv2_sub_events')
           ->where('id',$va)
           ->get()->row();
        }
      }
    }
    
    return $result;

  }

  public function event_summary_report(){

    return $this->db_readonly->select("emv.event_name, date_format(emv.reg_start_date,'%d-%m-%Y') as reg_start_date, registration_amount, safety_deposit_amount, max_number_of_allowed, organizer, count(emv.id) as reg_count")
    ->from('event_master_v2 emv')
    ->join('event_registrations er','emv.id=er.event_id')
    ->group_by('emv.id')
    ->get()->result();

  }

  public function get_student_wallet_summary_report($from_date, $to_date){

    return $this->db_readonly->select("emv.event_name, date_format(emv.reg_start_date,'%d-%m-%Y') as reg_start_date, registration_amount, safety_deposit_amount, max_number_of_allowed, organizer, count(emv.id) as reg_count")
    ->from('event_master_v2 emv')
    ->join('event_registrations er','emv.id=er.event_id')
    ->group_by('emv.id')
    ->get()->result();
    
  }

  public function return_wallet_student_listby_class($classId, $event_id){
    
    $this->db_readonly->select('sa.id as stdId')
    ->from('event_registrations er')
    ->where('er.safety_deposit_amount_status','collected')
    ->where('er.event_id', $event_id)
    ->join('student_admission sa','er.student_id=sa.id')
    ->where('sa.admission_status','2')
    ->join('student_year sy','sa.id=sy.student_admission_id')
    ->join('class c','sy.class_id=c.id')
    ->where('sy.promotion_status!=', '4')
    ->where('sy.promotion_status!=', '5')
    ->where('sy.acad_year_id',$this->yearId);    
    if($classId) {
      $this->db_readonly->where_in('sy.class_id',$classId);
    }
    if($this->current_branch) {
      $this->db_readonly->where('c.branch_id',$this->current_branch);
    }
    $this->db_readonly->order_by('c.id','sa.first_name');
    $result = $this->db_readonly->get()->result();
    $stdIds = [];
    foreach ($result as $key => $res) {
      array_push($stdIds, $res->stdId);
    }
    return $stdIds;
  }

  public function return_wallet_student_listby_admission($admin_no, $event_id){
    
    $this->db_readonly->select('sa.id as stdId')
    ->from('event_registrations er')
    ->where('er.safety_deposit_amount_status','collected')
    ->where('er.event_id', $event_id)
    ->join('student_admission sa','er.student_id=sa.id')
    ->where('sa.admission_no',$admin_no);
    $result = $this->db_readonly->get()->result();
    $stdIds = [];
    foreach ($result as $key => $res) {
      array_push($stdIds, $res->stdId);
    }
    return $stdIds;

  }

  public function return_wallet_student_listby_studentname($name, $event_id){
    $this->db_readonly->select('sa.id as stdId')
    ->from('event_registrations er')
    ->where('er.event_id', $event_id)
    ->where('er.safety_deposit_amount_status','collected')
    ->join('student_admission sa','er.student_id=sa.id');
    $this->db_readonly->where("(LOWER(sa.first_name) like '%$name%' OR (LOWER(sa.last_name) like '%$name%'))");
    $result = $this->db_readonly->get()->result();
    $stdIds = [];
    foreach ($result as $key => $res) {
      array_push($stdIds, $res->stdId);
    }
    return $stdIds;


  }
   public function get_registered_event_student_data($student_id, $event_id){

      $display_roll_no_with_student_name = $this->settings->getSetting('display_roll_no_with_student_name');
      if ($display_roll_no_with_student_name == 1) {
        $std_name = "CONCAT(sy.roll_no, ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
      } else {
        $std_name = "CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
      }  

    return $this->db_readonly->select("swt.amount as safety_deposit_amount, concat(ifnull(cs.class_name,''),'',ifnull(cs.section_name,'')) as sectionName, $std_name, sa.admission_no, sa.id as student_id, swt.running_balance_amount")
    ->from('student_wallet_transactions swt')
    ->where_in('swt.student_id',$student_id)
    ->where('swt.source_type','Event')
    ->join('student_admission sa','swt.student_id=sa.id')
    ->join('student_year sy',"sa.id=sy.student_admission_id and sy.acad_year_id=$this->yearId")
    ->join('class_section cs',"sy.class_section_id=cs.id and cs.is_placeholder!=1")
    ->join('event_registrations er','er.safety_depost_amount_wallet_tx_id = swt.id')
    ->where('er.event_id', $event_id)
    ->where('er.safety_deposit_amount_status','collected')
    ->get()->result();

  }

  public function student_wallet_running_balance_amount($student_id){
    $student_ids = implode(',', $student_id);

    $result = $this->db_readonly->query('select * from student_wallet_transactions 
    where id in (select max(id) from student_wallet_transactions where student_id in ('.$student_ids.')  and source_type = "Event" or source_type = "Wallet" group by student_id)');
    
    $stdRunBalAmount =[];
    foreach ($result->result() as $key => $val) {
      $stdRunBalAmount[$val->student_id] = $val->running_balance_amount;
    }
    return $stdRunBalAmount;
  }
  public function inert_return_wallet_data($data, $table_name){
    $this->db->insert($table_name, $data);
    return $this->db->insert_id();
  }

  public function update_return_event_transaction_data($wallt_safety_return_tx_Id,$std_id, $event_id, $returnStatus){
    $data = array(
      'safety_deposit_amount_return_tx_id' => $wallt_safety_return_tx_Id,
      'safety_deposit_amount_status' => $returnStatus,
    );

    $this->db->where('event_id', $event_id);
    $this->db->where('student_id', $std_id);
    return $this->db->update('event_registrations',$data);
  }

  public function get_all_sub_event_details($event_id){
    $subEvents =  $this->db_readonly->select("id, sub_event_name, max_registrations_allowed, sub_event_description, (case when sub_event_date = '1970-01-01' then 'NA' else date_format(sub_event_date,'%d-%m-%Y') end) as  sub_event_date, (case when start_time_of_registration = '00:00:00' then 'NA' else date_format(start_time_of_registration,'%h:%i %p') end) as start_time_of_registration, (case when end_time_of_registration = '00:00:00' then 'NA' else date_format(end_time_of_registration,'%h:%i %p') end) as end_time_of_registration, ifnull(message_to_display,'') as message_to_display ")
    ->from('eventv2_sub_events')
    ->where('event_id',$event_id)
    ->get()->result();


    $subRegister = $this->db->select('id as sub_event_id')->where('event_id',$event_id)->get('event_registrations')->result();
    
    if(!empty($subEvents)){
      foreach ($subEvents as $key => $subEvent) {
        $subEvent->already_register = 0;
        if (!empty($subRegister)) {
          foreach ($subRegister as $key => $reg) {
            $res = json_decode($reg->sub_event_id);
            if (in_array($subEvent->id, $res)) {
              $subEvent->already_register = 1;
            }
          }
        }
      }
    }

    return $subEvents;
  }

  public function insert_sub_eventDetails(){
    $input = $this->input->post();
    $data = array(
      'event_id' => $input['event_id'],
      'sub_event_name' => $input['sub_event_name'],
      'max_registrations_allowed' => $input['max_registrations_allowed'],
      'sub_event_description' => $input['sub_event_description'],
      'sub_event_date' => date('Y-m-d',strtotime($input['sub_event_date'])),
      'start_time_of_registration' => $input['start_time_of_registration'],
      'end_time_of_registration' => $input['end_time_of_registration'],
      'message_to_display' => $input['message_to_display']
    );
    return $this->db->insert('eventv2_sub_events',$data);
  }

  public function insert_sub_event_array_Details(){
    $input = $this->input->post();
    $csvDataArry = [];
    foreach ($input['sub_event_name'] as $key => $val) {
      $csvDataArry[] = array(
        'event_id' => $input['event_id'],
        'sub_event_name' => $val,
        'max_registrations_allowed' => $input['max_registrations_allowed'][$key],
        'sub_event_description' => $input['sub_event_description'][$key],
        'sub_event_date' => date('Y-m-d',strtotime($input['sub_event_date'][$key])),
        'start_time_of_registration' => $input['start_time_of_registration'][$key],
        'end_time_of_registration' => $input['end_time_of_registration'][$key],
        'message_to_display' => $input['message_to_display'][$key]
      );
    }
    return $this->db->insert_batch('eventv2_sub_events',$csvDataArry);
  }
  public function get_sub_event_details($event_id){
    return $this->db->select('id, sub_event_name')
    ->from('eventv2_sub_events')
    ->where('event_id',$event_id)
    ->get()->result();
  }

  public function get_sub_event_register_student_data($subeventId, $event_id){

    $subEvent = $this->db_readonly->select("er.sub_event_id,student_id, date_format(er.registered_on,'%d-%m-%Y %h:%i %p') as registered_date")
    ->from('event_registrations er')
    ->where('er.event_id', $event_id)
    ->get()->result();

    foreach ($subEvent as $key => $value) {
      $subeventIds = json_decode($value->sub_event_id);
      foreach ($subeventIds as $key => $val) {
        if (in_array($val, $subeventId)) {
          $value->subName[] = $this->db_readonly->select('sub_event_name')->from('eventv2_sub_events')->where('id',$val)->get()->row()->sub_event_name;
          
          $prefix_student_name = $this->settings->getSetting('prefix_student_name');
          $prefix_order_by = $this->settings->getSetting('prefix_order_by');

          $order_by = 'sa.first_name';
          if ($prefix_order_by == "roll_number") {
            $order_by = 'sy.roll_no';
          }else if($prefix_order_by == "enrollment_number"){
            $order_by = 'sa.enrollment_number';
          }else if($prefix_order_by == "admission_number"){
            $order_by = 'sa.admission_number';
          }else if($prefix_order_by == "alpha_rollnum"){
            $order_by = 'sy.alpha_rollnum';
          }

          if ($prefix_student_name == "roll_number") {
            $std_name = "CONCAT(if(sy.roll_no = 0, 'NA', sy.roll_no), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
          } else if ($prefix_student_name == "enrollment_number") {
            $std_name = "CONCAT(ifnull(sa.enrollment_number, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
          } else if ($prefix_student_name == "admission_number") {
            $std_name = "CONCAT(ifnull(sa.admission_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
          } else if ($prefix_student_name == "alpha_rollnum") {
              $std_name = "CONCAT(ifnull(sy.alpha_rollnum, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
          } else {
            $std_name = "CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
          }
          $value->students = $this->db_readonly->select("concat(ifnull(cs.class_name,''),'',ifnull(cs.section_name,'')) as sectionName, $std_name, sa.admission_no, sa.id as student_id")
          ->from('student_admission sa')
          ->join('student_year sy',"sa.id=sy.student_admission_id and sy.acad_year_id=$this->yearId")
          ->join('class_section cs',"sy.class_section_id=cs.id and cs.is_placeholder!=1")
          ->where('sa.id', $value->student_id)
          ->order_by($order_by)
          ->get()->row();
        }
      }
    }
    
    return $subEvent;

    //  $display_roll_no_with_student_name = $this->settings->getSetting('display_roll_no_with_student_name');
    //   if ($display_roll_no_with_student_name == 1) {
    //     $std_name = "CONCAT(sy.roll_no, ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
    //   } else {
    //     $std_name = "CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
    //   }
    // $result = $this->db_readonly->select("concat(ifnull(cs.class_name,''),'',ifnull(cs.section_name,'')) as sectionName, $std_name, sa.admission_no, sa.id as student_id, er.sub_event_id, 
    //   date_format(er.registered_on,'%d-%m-%Y %h:%i %p') as registered_date")
    // ->from('event_registrations er')
    // ->join('student_admission sa','er.student_id=sa.id')
    // ->join('student_year sy',"sa.id=sy.student_admission_id and sy.acad_year_id=$this->yearId")
    // ->join('class_section cs',"sy.class_section_id=cs.id and cs.is_placeholder!=1")
    // ->where('er.event_id', $event_id)
    // ->get()->result();
    // $studentData = [];
    // foreach ($result as $key => $val) {
    //   $res = json_decode($val->sub_event_id);
    //   foreach ($res as $key => $value) {
    //     $val->subName[$value] = $this->db_readonly->select('sub_event_name')->from('eventv2_sub_events')->where('id',$value)->get()->row()->sub_event_name;
    //   }
    // }
    // echo "<pre>"; print_r($result); die();

    // foreach ($result as $key => $val) {
    //   foreach ($val->subName as $subId => $value) {
    //    if (in_array($subId, $subeventId)) {
    //       $studentData[$value] = $val;
    //     }
    //   }
      
    // }
    // return $studentData;
  }

  public function delete_sub_event_by_id($sub_event_id){
    $this->db->where('id',$sub_event_id);
    return $this->db->delete('eventv2_sub_events');
  }

  // Get complete event details for registration (same as parent registration view)
  public function get_register_event_details($event_id) {
    // Get basic event details with formatted dates
    $event = $this->db->select("*, date_format(reg_start_date,'%d-%b-%Y') as registration_startdate, date_format(reg_end_date,'%d-%b-%Y') as registration_enddate")
      ->from('event_master_v2')
      ->where('id', $event_id)
      ->get()->row();

    if (!$event) {
      return null;
    }

    // Get event time details (agenda)
    $eventTime = $this->db->select("event_id, date_format(event_date,'%d-%b-%Y') as eventdate, time_format(start_time,'%h:%i %p') as starttime, time_format(end_time,'%h:%i %p') as endtime, description")
      ->from('event_v2_time_details')
      ->where('event_id', $event_id)
      ->get()->result();

    $event->event_date = $eventTime;

    // Get registration count
    $registerCount = $this->db->select('count(id) as register_count')
      ->where('event_id', $event_id)
      ->get('event_registrations')->row()->register_count;

    // Get sub event registrations count
    $subRegister = $this->db->select('sub_event_id')
      ->where('event_id', $event_id)
      ->get('event_registrations')->result();

    $subRegisterArray = [];
    foreach ($subRegister as $val) {
      if (array_key_exists($val->sub_event_id, $subRegisterArray)) {
        $subRegisterArray[$val->sub_event_id] = $subRegisterArray[$val->sub_event_id] + 1;
      } else {
        $subRegisterArray[$val->sub_event_id] = 1;
      }
    }

    $event->subRegister = $subRegisterArray;
    $event->register_count = $registerCount;

    return $event;
  }

  // Get sub events details (same as parent registration view)
  public function get_sub_events_details($event_id) {
    return $this->db_readonly->select("id, sub_event_name, max_registrations_allowed, sub_event_description, (case when sub_event_date = '1970-01-01' then '' else date_format(sub_event_date,'%d-%b-%Y') end) as sub_event_date, (case when start_time_of_registration = '00:00:00' then '' else date_format(start_time_of_registration,'%h:%i %p') end) as start_time_of_registration, (case when end_time_of_registration = '00:00:00' then '' else date_format(end_time_of_registration,'%h:%i %p') end) as end_time_of_registration")
      ->from('eventv2_sub_events')
      ->where('event_id', $event_id)
      ->get()->result();
  }

  // Helper method to get student details
  public function get_student_details($std_id) {
    $this->db->select("s.id,s.roll_no,s.admission_no, CONCAT(ifnull(s.first_name,''), ' ', ifnull(s.last_name,'')) as std_name,c.class_name,cs.section_name,s.gender,s.category,s.boarding,s.is_rte as rte,s.admission_type,s.board,s.medium,s.admission_status,c.id as class");
    $this->db->from('student s');
    $this->db->where('admission_status', '2'); // Approved 2
    $this->db->where('s.id', $std_id);
    $this->db->join('class c', 'c.id=s.class_id');
    $this->db->join('class_section cs', 's.class_section_id=cs.id', 'left');
    $data_info = $this->db->get()->row();
    $array = (array)$data_info;  //  use for object convert to array
    return $array;
  }

  public function get_wallet_balance_bystudentid($stdId){
    return $this->db->select('*')->where('student_id',$stdId)->get('student_wallet')->row();
  }

}

?>
