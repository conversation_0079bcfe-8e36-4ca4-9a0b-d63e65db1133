<ul class="breadcrumb">
    <li><a href="<?php echo site_url('avatars');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('feesv2/fees_dashboard');?>">Fee Dashboard</a></li>
    <li>Edit History Report</li>
</ul>
<div class="col-md-12">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px">
                <div class="col-md-10">
                    <h3 class="card-title panel_title_new_style_staff">
                        <a class="back_anchor" href="<?php echo site_url('feesv2/fees_dashboard');?>">
                            <span class="fa fa-arrow-left"></span>
                        </a>
                        Edit History Report
                    </h3>
                </div>

                <!--   <label class="checkbox-inline"><input style="width:20px;height: 20px;" type="checkbox" name="installment_search" id="installment_search"><span style="font-size:16px; margin-left: 10px;">Show Installments</span></label> -->

            </div>
        </div>
        <div class="card-body">

            <div class="col-md-3 form-group">
                <label class="col-md-5 control-label" >Filter By</label>
                <div class="form-group">
                    <div class="col-md-12">
                        <select required="" class="form-control" id="filter_by" name="" onchange="filter_by()">
                            <option value="date">Date Range</option>
                            <option value="name">Student Name</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="col-md-3 form-group" id="date">
            <label class="col-md-12 control-label p-0" >Date Range</label>
                <div id="reportrange" class="dtrange" style="width: 100%">                                            
                    <span></span>
                    <input type="hidden" id="from_date">
                    <input type="hidden" id="to_date">
                </div>
            </div>
            
            <div class="col-md-2 form-group" id="search_by_name" style="display: none;">
            <label class="col-md-12 control-label p-0" >Search By Name</label>
            <input type="text" name="by_name" id="by_name" class="form-control">
            </div>

            <!-- <div class="col-md-2 form-group">
                <label class="control-label" style="padding-left: 10px;">Class Section</label>
                <div class="form-group">
                    <div class="col-md-12">
                        <select required="" class="form-control" id="class_section" name="class_section">
                        </select>
                    </div>
                </div>
            </div>

            <div class="col-md-2 form-group">
                <label class="control-label" style="padding-left: 10px;">Student Name</label>
                <div class="form-group">
                    <div class="col-md-12">
                        <select required="" class="form-control" id="stud_id" name="stud_id">
                            <option value="">All Students</option>
                        </select>
                    </div>
                </div>
            </div> -->

            <div class="col-sm-2 col-md-2 d-flex align-items-end pl-0" style="height: 4.5rem;">
                <button class="btn btn-primary" id="search" type="button" onclick="get_fees_history_data();">Get
                    Report</button>
            </div>
        </div>
        <div class="card-body">
            <div class="col-md-12 table-responsive" id="history_table"></div>
        </div>
    </div>
</div>

<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>
    <script type="text/javascript" src="<?php echo base_url('assets/js/plugins/daterangepicker/daterangepicker.js') ?>"></script>

<script>
$(document).ready(function() {
    // get_class_section();
    get_fees_history_data();
});

function filter_by(){
    var filter_by = $('#filter_by').val();
    if(filter_by == 'name'){
        $('#date').hide();
        $('#search_by_name').show();
    }else{
        $('#date').show();
        $('#search_by_name').hide();
        $('#by_name').val(''); 
    }
}

$("#reportrange").daterangepicker({
      ranges: {
        'Today': [moment(), moment()],
        'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
        'Last 7 Days': [moment().subtract(6, 'days'), moment()],
        'Last 30 Days': [moment().subtract(29, 'days'), moment()],
        'This Month': [moment().startOf('month'), moment()],
        'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')],
      },
      opens: 'right',
      buttonClasses: ['btn btn-default'],
      applyClass: 'btn-small btn-primary',
      cancelClass: 'btn-small',
      format: 'DD-MM-YYYY',
      separator: ' to ',
      startDate: moment().subtract(6, 'days'),
      endDate: moment()            
    },function(start, end) {
      $('#reportrange span').html(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
      $('#from_date').val(start.format('DD-MM-YYYY'));
      $('#to_date').val(end.format('DD-MM-YYYY'));
    });
    
    $("#reportrange span").html(moment().subtract(6, 'days').format('MMM D, YYYY') + ' - ' + moment().format('MMM D, YYYY'));
    $('#from_date').val(moment().subtract(6, 'days').format('DD-MM-YYYY'));
    $('#to_date').val(moment().format('DD-MM-YYYY'));

function get_class_section() {
    $.ajax({
        url: '<?php echo site_url('reports/student/Student_docs_controller/get_class_section_names'); ?>',
        type: "post",
        success: function(data) {
            var clsSection = $.parseJSON(data);
            option = '<option value="">Class Section</option>';
            for (i = 0; i < clsSection.length; i++) {
                option += '<option value="' + clsSection[i].cId + '_' + clsSection[i].csId + '">' +
                    clsSection[i].classSection + '</option>'
            }
            $('#class_section').html(option);
        },

        error: function(err) {
            console.log(err);
        }
    });
}

$("#class_section").on('change', function() {
    // var cls_section = $('#class_section').val();
    $.ajax({
        url: '<?php echo site_url('reports/student/Student_docs_controller/get_class_section_student_data'); ?>',
        type: "post",
        data: {
            'cls_section': cls_section
        },
        success: function(data) {
            var stu_data = $.parseJSON(data);
            option = '';
            option += '<option value="">All Students</option>';
            for (i = 0; i < stu_data.length; i++) {
                option += '<option value="' + stu_data[i].sId + '">' + stu_data[i].sName +
                    '</option>'
            }
            $('#stud_id').html(option);
        },
        error: function(err) {
            console.log(err);
        }
    });
});

function get_fees_history_data() {
    // var cls_section = $('#class_section').val();
    var from_date = $('#from_date').val();
    var to_date = $('#to_date').val();
    var name = $('#by_name').val();
    // if (cls_section == '') {
    //     alert('Select Class Section');
    //     return false;
    // }
    // var stu_id = $('#stud_id').val();
    $.ajax({
        url: '<?php echo site_url('feesv2/reports_v2/get_fees_edit_history_data'); ?>',
        type: "post",
        data: {
            // 'cls_section': cls_section,
            // 'stu_id': stu_id
            'name': name,
            'from_date':from_date,
            'to_date':to_date
        },
        success: function(data) {
            var stu_data = $.parseJSON(data);
            if (stu_data != '') {
                $('#history_table').html(construct_table(stu_data));
            } else {
                $('#history_table').html('<h3 class="no-data-display">No Data Found</h3>');
            }

        },
        error: function(err) {
            console.log(err);
        }
    });
}

function construct_table(data){
    var html = '';
    html += `<table class="table table-bordered" id="data_table">
                <thead>
                <th>#</th>
                <th>Student Name</th>
                <th>Old Data</th>
                <th>Updated Data</th>
                <th>Edited By</th>
                <th>Edited On</th>
                </thead>
                <tbody>`;
                for(var i=0;i<data.length;i++){
                  html += '<tr>';
                  html += `<td>${i+1}</td>`;
                  html += `<td>${data[i].student_name}</td>`;
                  if(data[i].old_value == null){
                    data[i].old_value = '';
                  }
                  if(data[i].edited_by == ' '){
                    data[i].edited_by = 'Admin';
                  }
                  html += `<td>${data[i].old_value}</td>`;
                  html += `<td>${data[i].new_value}</td>`;
                  html += `<td>${data[i].edited_by}</td>`;
                  html += `<td>${data[i].edited_on}</td>`;
                  html += '</tr>';
                }
                html += '</tbody>';
                html += '</table>';

    return html;
}
</script>